2025/09/11 20:09:25 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 20:09:25 Index iac_git_commits exists - 
2025/09/11 20:09:25 Index cfstack_templates exists - 
2025/09/11 20:09:25 Index arm_templates exists - 
2025/09/11 20:09:25 Index terraform_resources exists - 
2025/09/11 20:09:25 Index tf_commits exists - 
2025/09/11 20:09:25 Index tf_variables exists - 
2025/09/11 20:09:26 Index resource_context exists - 
2025/09/11 20:09:26 Index text_lookup exists - 
2025/09/11 20:09:26 Index ai_resources exists - 
2025/09/11 20:09:26 Index idp_events exists - 
2025/09/11 20:09:26 Index idp_users exists - 
2025/09/11 20:09:26 Index idp_apps exists - 
2025/09/11 20:09:26 Index idp_groups exists - 
2025/09/11 20:09:27 Index cloud_incidents exists - 
2025/09/11 20:09:27 Index jira_issues exists - 
2025/09/11 20:09:27 Index jira_data exists - 
2025/09/11 20:09:27 Index jira_resources exists - 
2025/09/11 20:09:27 Index precize_creations exists - 
2025/09/11 20:09:27 Index external_cloud_resources exists - 
2025/09/11 20:09:27 Initializing Weaviate client... - 
2025/09/11 20:09:27 Weaviate connection status  - true - 
2025/09/11 20:09:27 Starting server on port 19090 - 
2025/09/11 20:10:29 [51] [{no such prop with name 'class' found in class 'Sample' in the schema. Check your schema files for which properties in this class are available}] Some objects failed to insert: %v - 
goroutine 51 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x10125b9ea, 0x5}, {0x140002400c0, 0xbc}, {0x14000655200, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate/service.(*WeaviateService).InsertDataByID(0x14000016080, {0x1016dd470, 0x140002a9230}, {{0x140004ef608, 0x6}, {0x1400018c6e0, 0x1, 0x1}})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/service/data.go:137 +0xab8
github.com/precize/pserver/server/weaviate/controller.(*WeaviateController).InsertDataByID(0x14000016088, {0x1016dbc10, 0x140002920e0}, 0x140006583c0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/controller/data.go:109 +0x290
net/http.HandlerFunc.ServeHTTP(0x1400021c6b0, {0x1016dbc10, 0x140002920e0}, 0x140006583c0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x1016dbc10, 0x140002920e0}, 0x140006583c0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:70 +0x4c
net/http.HandlerFunc.ServeHTTP(0x1400000c2b8, {0x1016dbc10, 0x140002920e0}, 0x140006583c0)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x14000126000, {0x1016dbc10, 0x140002920e0}, 0x140006583c0)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x1400020c3c0}, {0x1016dbc10, 0x140002920e0}, 0x14000658140)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x1400019bb00, {0x1016dd4a8, 0x1400029a7d0})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
2025/09/11 20:12:40 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 20:12:40 Index iac_git_commits exists - 
2025/09/11 20:12:40 Index cfstack_templates exists - 
2025/09/11 20:12:40 Index arm_templates exists - 
2025/09/11 20:12:40 Index terraform_resources exists - 
2025/09/11 20:12:40 Index tf_commits exists - 
2025/09/11 20:12:40 Index tf_variables exists - 
2025/09/11 20:12:40 Index resource_context exists - 
2025/09/11 20:12:40 Index text_lookup exists - 
2025/09/11 20:12:40 Index ai_resources exists - 
2025/09/11 20:12:40 Index idp_events exists - 
2025/09/11 20:12:40 Index idp_users exists - 
2025/09/11 20:12:41 Index idp_apps exists - 
2025/09/11 20:12:41 Index idp_groups exists - 
2025/09/11 20:12:41 Index cloud_incidents exists - 
2025/09/11 20:12:41 Index jira_issues exists - 
2025/09/11 20:12:41 Index jira_data exists - 
2025/09/11 20:12:41 Index jira_resources exists - 
2025/09/11 20:12:41 Index precize_creations exists - 
2025/09/11 20:12:41 Index external_cloud_resources exists - 
2025/09/11 20:12:41 Initializing Weaviate client... - 
2025/09/11 20:12:41 Weaviate connection status  - true - 
2025/09/11 20:12:41 Starting server on port 19090 - 
2025/09/11 20:14:14 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "qa-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 20:14:14 Index iac_git_commits exists - 
2025/09/11 20:14:15 Index cfstack_templates exists - 
2025/09/11 20:14:15 Index arm_templates exists - 
2025/09/11 20:14:15 Index terraform_resources exists - 
2025/09/11 20:14:15 Index tf_commits exists - 
2025/09/11 20:14:15 Index tf_variables exists - 
2025/09/11 20:14:15 Index resource_context exists - 
2025/09/11 20:14:15 Index text_lookup exists - 
2025/09/11 20:14:15 Index ai_resources exists - 
2025/09/11 20:14:15 Index idp_events exists - 
2025/09/11 20:14:15 Index idp_users exists - 
2025/09/11 20:14:15 Index idp_apps exists - 
2025/09/11 20:14:15 Index idp_groups exists - 
2025/09/11 20:14:15 Index cloud_incidents exists - 
2025/09/11 20:14:15 Index jira_issues exists - 
2025/09/11 20:14:15 Index jira_data exists - 
2025/09/11 20:14:15 Index jira_resources exists - 
2025/09/11 20:14:15 Index precize_creations exists - 
2025/09/11 20:14:15 Index external_cloud_resources exists - 
2025/09/11 20:14:15 Initializing Weaviate client... - 
2025/09/11 20:14:16 Weaviate connection status  - true - 
2025/09/11 20:14:16 Starting server on port 19090 - 
2025/09/11 20:14:19 [12] Failed to parse UUID: %v - invalid UUID length: 23 - 
goroutine 12 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x1054538ea, 0x5}, {0x1400053cec0, 0x3a}, {0x1400004f200, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/pserver/server/weaviate/service.(*WeaviateService).InsertDataByID(0x1400011a0d0, {0x1058d5470, 0x14000140030}, {{0x140002727b0, 0x6}, {0x1400011a198, 0x1, 0x1}})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/service/data.go:96 +0x288
github.com/precize/pserver/server/weaviate/controller.(*WeaviateController).InsertDataByID(0x1400011a0d8, {0x1058d3c10, 0x1400020a000}, 0x14000131040)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/weaviate/controller/data.go:109 +0x290
net/http.HandlerFunc.ServeHTTP(0x14000496aa0, {0x1058d3c10, 0x1400020a000}, 0x14000131040)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/precize/pserver/server.verifyMiddleware.func1({0x1058d3c10, 0x1400020a000}, 0x14000131040)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/pserver/server/pserver.go:70 +0x4c
net/http.HandlerFunc.ServeHTTP(0x1400062e2a0, {0x1058d3c10, 0x1400020a000}, 0x14000131040)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2220 +0x40
github.com/gorilla/mux.(*Router).ServeHTTP(0x140000ec300, {0x1058d3c10, 0x1400020a000}, 0x14000131040)
	/Users/<USER>/go/pkg/mod/github.com/gorilla/mux@v1.8.1/mux.go:212 +0x254
net/http.serverHandler.ServeHTTP({0x140001883c0}, {0x1058d3c10, 0x1400020a000}, 0x140001aab40)
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3210 +0x2b0
net/http.(*conn).serve(0x140004a2750, {0x1058d54a8, 0x140002144b0})
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:2092 +0x1604
created by net/http.(*Server).Serve in goroutine 1
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/net/http/server.go:3360 +0x88c
