package common

import (
	"bytes"
	"crypto/sha256"
	"encoding/base64"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/mail"
	"os"
	"path/filepath"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/precize/elastic"
	"github.com/precize/logger"
	"golang.org/x/text/cases"
	"golang.org/x/text/language"
)

const (
	EX_EMPLOYEE_PREFIX      = "[X] "
	INVALID_EMPLOYEE_PREFIX = "[I] "
)

func DateTime(date time.Time) string {
	return elastic.DateTime(date)
}

func GetAccountAndRegionFromArn(arn string) (account, region string) {

	splitArn := strings.Split(arn, ":")

	if len(splitArn) > 4 {
		account = splitArn[4]
		region = splitArn[3]
	}

	return
}

func GetSubscriptionFromResourceID(id string) (subscription string) {

	splitID := strings.Split(id, "/")

	if len(splitID) > 2 {
		subscription = splitID[2]
	}

	return
}

func GenerateCombinedHashID(fields ...string) string {

	var hashString string

	for i, field := range fields {
		hashString = hashString + strings.ToLower(field)
		if i != len(fields)-1 {
			hashString = hashString + "#"
		}
	}

	hash := HashSha256([]byte(hashString))

	if len(hash) > 20 {
		hash = hash[:20]
	}

	return hash
}

func GenerateCombinedHashIDCaseSensitive(fields ...string) string {

	var hashString string

	for i, field := range fields {
		hashString = hashString + field
		if i != len(fields)-1 {
			hashString = hashString + "#"
		}
	}

	hash := HashSha256([]byte(hashString))

	if len(hash) > 20 {
		hash = hash[:20]
	}

	return hash
}

func HashSha256(str []byte) string {

	sum := sha256.Sum256(str)
	return fmt.Sprintf("%x", sum)
}

func GetMinifiedJSONFileHash(jsonContent []byte) (fileHash string, err error) {

	var i any
	if err = json.Unmarshal(bytes.ToLower(jsonContent), &i); err != nil {
		return
	}

	sortedJsonContent, err := json.Marshal(i)
	if err != nil {
		return
	}

	dst := &bytes.Buffer{}

	if err = json.Compact(dst, sortedJsonContent); err != nil {
		return
	}

	fileHash = HashSha256(dst.Bytes())
	return
}

func GetFormattedNameFromEmail(email string) string {

	var (
		emailPrefix    = strings.ToLower(strings.Split(email, "@")[0])
		charsToReplace = "-_." // More likely to be in email (lesser processing compared to regex with all special chars)
	)

	for _, char := range charsToReplace {
		emailPrefix = strings.ReplaceAll(emailPrefix, string(char), " ")
	}

	return ConvertToTitleCase(emailPrefix)
}

func GetEmailNameWithoutSpecialCharacters(email string) string {
	emailPrefix := strings.ToLower(strings.Split(email, "@")[0])
	return RemoveSpecialCharactersFromString(emailPrefix)
}

func RemoveSpecialCharactersFromString(str string) string {
	regex := regexp.MustCompile(`[^a-zA-Z0-9]`)
	return regex.ReplaceAllString(str, "")
}

func SplitStringBySpecialCharacters(str string) []string {
	regex := regexp.MustCompile(`[^a-zA-Z0-9]+`)
	return regex.Split(str, -1)
}

func UniqueSplitStringBySpecialCharacters(str string) []string {
	regex := regexp.MustCompile(`[^a-zA-Z0-9]+`)
	parts := regex.Split(str, -1)

	seen := make(map[string]bool)
	var result []string

	for _, part := range parts {
		if part != "" && !seen[part] {
			seen[part] = true
			result = append(result, part)
		}
	}

	return result
}

func RemoveSpecialCharactersFromStringWithSpace(str string) string {
	regex := regexp.MustCompile(`[^a-zA-Z0-9]`)
	return regex.ReplaceAllString(str, " ")
}

func GetFormattedName(s string) string {
	s = RemoveSpecialCharactersFromStringWithSpace(s)
	return ConvertToTitleCase(s)
}

func ModifyGCPLabelToHandleConstraints(str string) string {
	// We have to modify GCP label due to constraints from GCP. YOR pipeline modifies it, so we match it with same modifications
	str = strings.ToLower(str)
	str = strings.ReplaceAll(str, "/", "__")
	str = strings.ReplaceAll(str, ".", "_")
	str = regexp.MustCompile(`[^\p{Ll}\p{Lo}\p{N}_-]`).ReplaceAllString(str, "")

	return str
}

func ReplaceSpecialCharactersWithChar(input string, replace string) string {
	regex := regexp.MustCompile(`[^a-zA-Z0-9]+`)
	result := regex.ReplaceAllString(input, replace)

	return result
}

// Not being used today
// func matchCommonSubstring(str1, str2 string) int {
// 	// Check if two strings have any common substrings of atleast 5 characters

// 	var (
// 		maxMatchedLength int
// 		substrings1      []string
// 		minLength        = 5
// 	)

// 	for i := range str1 {
// 		for j := i + (minLength - 1); j <= len(str1); j++ {
// 			substring := str1[i:j]
// 			if len(substring) >= minLength {
// 				substrings1 = append(substrings1, substring)
// 			}
// 		}
// 	}

// 	for _, substring := range substrings1 {
// 		if strings.Contains(str2, substring) {
// 			if len(substring) > maxMatchedLength {
// 				maxMatchedLength = len(substring)
// 			}
// 		}
// 	}

// 	return maxMatchedLength
// }

func ParseString(snakeCase string) string {
	re := regexp.MustCompile("(_[a-z])")

	camelCase := re.ReplaceAllStringFunc(snakeCase, func(match string) string {
		return strings.ToUpper(string(match[1]))
	})

	return camelCase
}

func equalVal(v any, val any) bool {
	// TODO: compare unordered maps
	switch v.(type) {
	case []any, map[any]any, map[string]any:
		if fmt.Sprintf("%v", v) == fmt.Sprintf("%v", val) {
			return true
		}
		return false
	}

	switch val.(type) {
	case []any, map[any]any, map[string]any:
		if fmt.Sprintf("%v", v) == fmt.Sprintf("%v", val) {
			return true
		}
		return false
	}

	if v == val {
		return true
	}
	switch val := val.(type) {
	case int32:
		if v == float32(val) {
			return true
		}
	case int64:
		if v == float64(val) {
			return true
		}
	case int:
		if v == float64(val) {
			return true
		}
	case string:
		switch v := v.(type) {
		case string:
			if strings.ToLower(v) == strings.ToLower(val) {
				return true
			}
		}
	case float64:
		if v == int(val) {
			return true
		}
	default:
		if fmt.Sprintf("%v", v) == fmt.Sprintf("%v", val) {
			return true
		}
	}

	return false
}

func equalContains(shortVal any, longVal any, isStaticMatch bool) bool {
	if fmt.Sprintf("%v", shortVal) == fmt.Sprintf("%v", longVal) {
		return true
	} else if shortVal == longVal {
		return true
	}
	switch longVal := longVal.(type) {
	case int32:
		if shortVal == float32(longVal) {
			return true
		}
	case int64:
		if shortVal == float64(longVal) {
			return true
		}
	case int:
		if shortVal == float64(longVal) {
			return true
		}
	case float64:
		if shortVal == int(longVal) {
			return true
		}
	case string:
		if !isStaticMatch {
			if shortValStr, ok := shortVal.(string); ok {
				if longVal != "" && shortValStr != "" {
					if strings.Contains(strings.ToLower(longVal), strings.ToLower(shortValStr)) && len(shortValStr) > 1 {
						return true
					} else if strings.Contains(strings.ToLower(shortValStr), strings.ToLower(longVal)) && len(longVal) > 1 {
						return true
					}
				}
			}
		} else {
			if shortValStr, ok := shortVal.(string); ok {
				if longVal != "" && shortValStr != "" {
					if strings.HasSuffix(longVal, shortValStr) {
						return true
					}
				}
			}
		}
	}

	return false
}

func contains(arr []any, val any) bool {
	if _, ok := val.([]any); ok {
		return false
	}
	for _, v := range arr {
		if equalVal(v, val) {
			return true
		}
	}
	return false
}

func arrContains(arr []any, val []any) bool {
	for _, item := range arr {
		if subArr, isSubArray := item.([]any); isSubArray {
			if len(subArr) == len(val) {
				equal := true
				for i, subItem := range subArr {
					if !equalVal(subItem, val[i]) {
						equal = false
						break
					}
				}
				if equal {
					return true
				}
			}
		}
	}
	return false
}

func arrContainsPartial(arr []any, val []any, isDynamicVar bool, matchingCount *int, tfVariablesMapping map[string]any, variableCommitIDMap map[any][]string, commitIDs map[string]struct{}, isStaticMatch bool, lessValueMatch *bool) bool {
	equal := false
	for _, item := range arr {
		for _, v := range val {
			if vArr, ok := v.([]any); ok {
				isMatchFound := false
				for _, v1 := range vArr {
					if isDynamicVar {
						cloudPropertyValue := fmt.Sprintf("%v", item)
						if valStr, ok := v1.(string); ok {

							modifiedVals := FindStringCombinationsForVars(valStr, tfVariablesMapping, variableCommitIDMap)
							if len(modifiedVals) == 0 {
								modifiedVals[valStr] = []string{}
							}
							for modifiedValskey, modifiedValsVal := range modifiedVals {
								if equalContains(cloudPropertyValue, modifiedValskey, isStaticMatch) {
									for _, id := range modifiedValsVal {
										commitIDs[id] = struct{}{}
									}
									isMatchFound = true
									equal = true
									break
								}
								modifiedVal := RemoveVarsFromString(modifiedValskey)
								for _, v := range modifiedVal {
									if equalContains(cloudPropertyValue, v, isStaticMatch) {
										for _, id := range modifiedValsVal {
											commitIDs[id] = struct{}{}
										}
										isMatchFound = true
										equal = true
										break
									}
								}
							}
							if !isMatchFound {
								modifiedVal := RemoveVarsFromString(valStr)
								for _, val := range modifiedVal {
									if equalContains(cloudPropertyValue, val, isStaticMatch) {
										isMatchFound = true
										equal = true
										break
									} else if len(val) <= 1 {
										isMatchFound = true
										equal = true
										if lessValueMatch != nil {
											*lessValueMatch = true
										}
									}
								}
							}
						}
					} else {
						if equalVal(item, v1) {
							equal = true
							break
						}
					}
				}

				if !isMatchFound {
					return false
				} else {
					return true
				}
			} else {
				if isDynamicVar {
					isMatchFound := false
					cloudPropertyValue := fmt.Sprintf("%v", item)
					if valStr, ok := v.(string); ok {
						modifiedVals := FindStringCombinationsForVars(valStr, tfVariablesMapping, variableCommitIDMap)
						if len(modifiedVals) == 0 {
							modifiedVals[valStr] = []string{}
						}
						for modifiedValskey, modifiedValsVal := range modifiedVals {
							if equalContains(cloudPropertyValue, modifiedValskey, isStaticMatch) {
								for _, id := range modifiedValsVal {
									commitIDs[id] = struct{}{}
								}
								isMatchFound = true
								equal = true
								*matchingCount++
								break
							}
							modifiedVal := RemoveVarsFromString(modifiedValskey)
							if len(modifiedVal) == 0 {
								isMatchFound = true
							}
							for _, v := range modifiedVal {
								if equalContains(cloudPropertyValue, v, isStaticMatch) {
									for _, id := range modifiedValsVal {
										commitIDs[id] = struct{}{}
									}
									isMatchFound = true
									equal = true
									break
								} else if len(v) <= 1 {
									isMatchFound = true
									equal = true
									if lessValueMatch != nil {
										*lessValueMatch = true
									}
								}
							}
						}
						if !isMatchFound {
							modifiedVal := RemoveVarsFromString(valStr)
							if len(modifiedVal) == 0 {
								isMatchFound = true
							}
							for _, val := range modifiedVal {
								if equalContains(cloudPropertyValue, val, isStaticMatch) {
									isMatchFound = true
									equal = true
									break
								} else if len(val) <= 1 {
									isMatchFound = true
									equal = true
									if lessValueMatch != nil {
										*lessValueMatch = true
									}
								}
							}
						}
						if !isMatchFound {
							return false
						} else {
							return true
						}
					}
				} else {
					if equalVal(item, v) {
						equal = true
						break
					}
				}
			}
		}
	}
	return equal
}

func RemoveVarsFromString(str string) []string {
	arr := make([]string, 0)
	word := ""
	isOpen := false
	for _, s := range str {
		if string(s) == "{" {
			if len(word) > 0 {
				arr = append(arr, word)
				word = ""
			}
			isOpen = true
		} else if string(s) == "}" {
			isOpen = false
			continue
		}
		if !isOpen {
			word = word + string(s)
		}
	}
	if len(word) > 0 {
		arr = append(arr, word)
	}
	return arr
}

// not being used
// func removeDuplicate[T comparable](sliceList []T) []T {
// 	allKeys := make(map[T]bool)
// 	list := []T{}
// 	for _, item := range sliceList {
// 		if _, value := allKeys[item]; !value {
// 			allKeys[item] = true
// 			list = append(list, item)
// 		}
// 	}
// 	return list
// }

func SeparateCamelCaseWithSpecialChar(camelCase string, specialChar string) string {
	re := regexp.MustCompile(`([a-z])([A-Z])`)
	result := re.ReplaceAllString(camelCase, "$1"+specialChar+"$2")
	return result
}

func EncodeBase64(str string) string {
	return base64.StdEncoding.EncodeToString([]byte(str))
}

func FetchValueFromPath(path string, key string) string {
	parts := strings.Split(path, "/")

	var value string
	for i, part := range parts {
		if part == key {
			value = parts[i+1]
			break
		}
	}
	return value
}

func FindStringCombinationsForVars(str string, tfVariablesMapping map[string]any, variableCommitIDMap map[any][]string) map[string][]string {
	combinations := make(map[string][]string, 0)
	commitIds := make([]string, 0)
	findCombinations(str, 0, "", tfVariablesMapping, combinations, variableCommitIDMap, commitIds)
	return combinations
}

func findCombinations(str string, index int, current string, tfVariablesMapping map[string]any, combinations map[string][]string, variableCommitIDMap map[any][]string, commitIds []string) {
	if index == len(str) {
		ids := make([]string, len(commitIds))
		copy(ids, commitIds)
		combinations[current] = ids
		return
	}

	if string(str[index]) == "{" {
		varName := ""
		for i := index + 1; i < len(str); i++ {
			if string(str[i]) == "}" {
				if valueArr, ok := tfVariablesMapping[varName].([]any); ok {
					for _, val := range valueArr {
						switch val := val.(type) {
						case string:
							if ids, ok := variableCommitIDMap[val]; ok {
								commitIds = append(commitIds, ids...)
							}
							findCombinations(str, i+1, current+val, tfVariablesMapping, combinations, variableCommitIDMap, commitIds)
							if len(commitIds) > 0 {
								commitIds = commitIds[:len(commitIds)-1] // Remove the last element if commitIds is non-empty
							}
						case int, float64, bool:
							if ids, ok := variableCommitIDMap[val]; ok {
								commitIds = append(commitIds, ids...)
							}
							findCombinations(str, i+1, current+fmt.Sprintf("%v", val), tfVariablesMapping, combinations, variableCommitIDMap, commitIds)
							if len(commitIds) > 0 {
								commitIds = commitIds[:len(commitIds)-1] // Remove the last element if commitIds is non-empty
							}
						}
					}
				} else {
					// If the variable is not found, keep it as is
					findCombinations(str, i+1, current+"{var."+varName+"}", tfVariablesMapping, combinations, variableCommitIDMap, commitIds)
				}
				break
			} else if string(str[i]) == "." {
				varName = ""
			} else {
				varName += string(str[i])
			}
		}
	} else {
		findCombinations(str, index+1, current+string(str[index]), tfVariablesMapping, combinations, variableCommitIDMap, commitIds)
	}
}

func FetchTenantTfApproach(tenantId string) (string, error) {
	searchQuery := `{"query":{"bool":{"filter":[{"match": {"tenantId.keyword": "` + tenantId + `"}},{"match": {"derivedFrom": "yor"}},{"terms": {"userType.keyword": ["gitlab","github","bitbucket"]}}]}},"size": 1}`
	resourceUserEventDocs, err := elastic.ExecuteSearchQueryWithoutPagination([]string{elastic.RESOURCE_USER_EVENTS_INDEX}, searchQuery)
	if err != nil {
		return "", err
	}

	if len(resourceUserEventDocs) > 0 {
		return "yor", nil
	} else {
		return "tfAlternate", nil
	}
}

func GetAbsoluteFilePath(srcPath, destPath string) string {
	dir := filepath.Dir(srcPath)
	absNewPath := filepath.Join(dir, destPath)
	return absNewPath
}

func FetchTenantAccountIds(tenantID string, serviceID string) ([]string, error) {
	accountIds := make([]string, 0)
	query := `
	{
		"query": {
		  "bool": {
			"must": [
			  {
				"match": {
				  "tenantId": "` + tenantID + `"
				}
			  },
			  {
				"term": {
				  "serviceId": "` + serviceID + `"
				}
			  }
			],
			"must_not": [],
			"should": []
		  }
		},
		"from": 0,
		"aggs": {
			"byAccount": {
				"terms": {
				"field": "accountId.keyword"
				}
			}
		}
	}
	`

	cloudStoreAggregation, err := elastic.ExecuteSearchForAggregation([]string{elastic.CLOUD_RESOURCE_STORE_INDEX}, query)
	if err != nil {
		return nil, err
	}
	if filePathAggregation, ok := cloudStoreAggregation["byAccount"].(map[string]any); ok {

		if filePathBuckets, ok := filePathAggregation["buckets"].([]any); ok {

			for _, filePathBucket := range filePathBuckets {

				if filePathBucket, ok := filePathBucket.(map[string]any); ok {

					if accountId, ok := filePathBucket["key"].(string); ok {
						accountIds = append(accountIds, accountId)

					}
				}
			}
		}
	}
	return accountIds, nil
}

func ConvertToTitleCase(input any) string {
	stringValue := fmt.Sprintf("%v", input)
	caser := cases.Title(language.English)
	titlecased := caser.String(stringValue)

	return titlecased
}

func RemoveHTMLTagsFromString(input string) string {
	re := regexp.MustCompile(`<[^>]*>`)
	output := re.ReplaceAllString(input, "")
	return output
}

func Base64Decode(str string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		logger.Print(logger.ERROR, "Unable to decode base64 encoded string", str)
		return "", err
	}
	return string(data), nil
}

func CalculateMean(data []float64) float64 {
	sum := 0.0
	for _, val := range data {
		sum += val
	}
	return sum / float64(len(data))
}

func UnescapeString(s string) string {
	s = strings.ReplaceAll(s, `\"`, `"`)
	return s[1 : len(s)-1]
}

func EscapeString(s string) string {
	var re *regexp.Regexp

	re = regexp.MustCompile(`\\`)
	s = re.ReplaceAllString(s, `\\`)

	re = regexp.MustCompile(`"`)
	s = re.ReplaceAllString(s, `\"`)

	return s
}

func ParseAddress(email string) (*mail.Address, error) {

	var isExUser bool
	if strings.Contains(email, EX_EMPLOYEE_PREFIX) {
		isExUser = true
		email = strings.Trim(email, EX_EMPLOYEE_PREFIX)
	}

	var isInvalidEmail bool
	if strings.Contains(email, INVALID_EMPLOYEE_PREFIX) {
		isInvalidEmail = true
		email = strings.Trim(email, INVALID_EMPLOYEE_PREFIX)
	}

	email = strings.ToLower(email)
	addr, err := mail.ParseAddress(email)
	if err != nil {
		return nil, fmt.Errorf("invalid email: %v", email)
	}

	if len(addr.Address) > 0 {
		strs := strings.Split(addr.Address, "@")
		if len(strs) > 1 && !strings.Contains(strs[1], ".") {
			return nil, fmt.Errorf("invalid email: %v", email)
		}
	}

	if isExUser {
		addr.Name = EX_EMPLOYEE_PREFIX + addr.Name
	}

	if isInvalidEmail {
		addr.Name = INVALID_EMPLOYEE_PREFIX + addr.Name
	}

	addr.Name = ConvertToTitleCase(addr.Name)
	return addr, nil
}

func CreateCSVFile(fileName string, data [][]string) error {

	file, err := os.Create(fileName)
	if err != nil {
		logger.Print(logger.ERROR, "Failed to create file")
		return err
	}

	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	writer.WriteAll(data)

	return nil
}

func HasAnySuffix(s string, suffixes []string) bool {
	for _, suffix := range suffixes {
		if strings.HasSuffix(s, suffix) {
			return true
		}
	}
	return false
}

func RemoveSuffixes(s string, suffixes []string) string {
	for _, suffix := range suffixes {
		trimmedStr := strings.TrimSuffix(s, suffix)
		if trimmedStr != s {
			return trimmedStr
		}
	}
	return s
}

func FormatElasticTime(timeStr string) (string, error) {
	_, err := time.Parse(elastic.DATE_FORMAT, timeStr)
	if err == nil {
		return timeStr, nil
	}

	t, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return "", err
	}
	return t.Format(elastic.DATE_FORMAT), nil
}

func ConvertTime(inputTime, inputFormat, outputFormat string) (string, error) {
	parsedTime, err := time.Parse(inputFormat, inputTime)
	if err != nil {
		logger.Print(logger.ERROR, "Error parsing time ", err)
		return "", err
	}

	return parsedTime.Format(outputFormat), nil
}

func RecursiveLookupInMap(m map[string]string, key string) string {
	value, exists := m[key]
	if !exists {
		return key
	}

	if value == key {
		return value
	}

	return RecursiveLookupInMap(m, value)
}

const lettersForRandom = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func SimpleRandomString(n int) string {
	rand.Seed(time.Now().UnixNano())
	result := make([]byte, n)
	for i := range result {
		result[i] = lettersForRandom[rand.Intn(len(lettersForRandom))]
	}
	return string(result)
}

func GenerateCombinations(tags []string, k int) [][]string {
	var res [][]string
	var comb func(start int, path []string)
	comb = func(start int, path []string) {
		if len(path) == k {
			// Make a copy to avoid reuse
			combination := make([]string, k)
			copy(combination, path)
			res = append(res, combination)
			return
		}
		for i := start; i < len(tags); i++ {
			comb(i+1, append(path, tags[i]))
		}
	}
	comb(0, []string{})
	return res
}

func ConvertToStringSlice(ports any) []string {
	if ports == nil {
		return []string{}
	}

	switch v := ports.(type) {
	case int:
		return []string{strconv.Itoa(v)}

	case float64:
		return []string{strconv.Itoa(int(v))}

	case []any:
		result := make([]string, 0, len(v))
		for _, port := range v {
			switch p := port.(type) {
			case int:
				result = append(result, strconv.Itoa(p))
			case float64:
				result = append(result, strconv.Itoa(int(p)))
			case string:
				result = append(result, p)
			}
		}
		return result

	case []int:
		result := make([]string, 0, len(v))
		for _, port := range v {
			result = append(result, strconv.Itoa(port))
		}
		return result

	case []string:
		return v

	default:
		logger.Print(logger.INFO, "Unexpected type for ports %v %v\n", ports, reflect.TypeOf(ports))
		return []string{}
	}
}

func isAlphaNumeric(c byte) bool {
	return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9')
}

func MatchWordBoundary(val, str2 string) bool {
	valLen := len(val)
	str2Len := len(str2)

	for i := 0; i <= str2Len-valLen; i++ {
		if str2[i:i+valLen] == val {
			leftBoundary := i == 0 || !isAlphaNumeric(str2[i-1])
			rightBoundary := i+valLen == str2Len || !isAlphaNumeric(str2[i+valLen])

			if leftBoundary && rightBoundary {
				return true
			}
		}
	}
	return false
}

func SplitByNonAlphaNumericEfficient(str string) []string {
	if len(str) == 0 {
		return []string{}
	}

	var result []string
	start := -1

	for i := 0; i < len(str); i++ {
		c := str[i]

		if isAlphaNumeric(c) {
			if start == -1 {
				start = i
			}
		} else {
			if start != -1 {

				result = append(result, str[start:i])
				start = -1
			}
		}
	}

	if start != -1 {
		result = append(result, str[start:])
	}

	return result
}


func RemoveDuplicatesFromStringSlice(strSlice []string) []string {
	if len(strSlice) == 0 {
		return strSlice
	}

	keys := make(map[string]bool)
	result := []string{}

	for _, item := range strSlice {
		if _, exists := keys[item]; !exists {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}
