2025/09/11 17:13:42 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 17:13:42 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 17:13:42 Index iac_git_commits exists - 
2025/09/11 17:13:42 Index cfstack_templates exists - 
2025/09/11 17:13:42 Index arm_templates exists - 
2025/09/11 17:13:42 Index terraform_resources exists - 
2025/09/11 17:13:42 Index tf_commits exists - 
2025/09/11 17:13:42 Index tf_variables exists - 
2025/09/11 17:13:42 Index resource_context exists - 
2025/09/11 17:13:42 Index text_lookup exists - 
2025/09/11 17:13:42 Index ai_resources exists - 
2025/09/11 17:13:42 Index idp_events exists - 
2025/09/11 17:13:43 Index idp_users exists - 
2025/09/11 17:13:43 Index idp_apps exists - 
2025/09/11 17:13:43 Index idp_groups exists - 
2025/09/11 17:13:43 Index cloud_incidents exists - 
2025/09/11 17:13:43 Index jira_issues exists - 
2025/09/11 17:13:43 Index jira_data exists - 
2025/09/11 17:13:43 Index jira_resources exists - 
2025/09/11 17:13:43 Index precize_creations exists - 
2025/09/11 17:13:43 Index external_cloud_resources exists - 
2025/09/11 17:13:43 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 17:13:43 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 17:13:43 Got error sending request - Get "http://localhost:9996/precize/private/0R8Da4gBoELr5xpoQ6Y3/cred": read tcp [::1]:54924->[::1]:9996: read: connection reset by peer - 
goroutine 1 [running]:
runtime/debug.Stack()
	/Users/<USER>/go/pkg/mod/golang.org/<EMAIL>-arm64/src/runtime/debug/stack.go:26 +0x64
github.com/precize/logger.Print({0x100deb2f1, 0x5}, {0x140002460b0, 0xa6}, {0x1400003d128, 0x1, 0x1})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/logger/log.go:121 +0x950
github.com/precize/transport.SendRequest({0x100deac9b, 0x3}, {0x140000286c0, 0x3f}, 0x0, 0x1400003d430, {0x0, 0x0}, {0x0, 0x0, ...})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/transport/client.go:125 +0x710
github.com/precize/transport.SendRequestToServer({0x100deac9b, 0x3}, {0x140000286c0, 0x3f}, 0x0, {0x0, 0x0})
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/transport/client.go:90 +0x210
github.com/precize/provider/tenant.GetTenantData({_, _}, _)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/provider/tenant/tenant.go:210 +0x150
github.com/precize/enhancer/enhance.StartContextProcessing({0x100df7ab9, 0x14}, {0x100df07b9, 0xd}, {0x100deaecc, 0x4}, 0x0)
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/enhancer/enhance/enhance.go:57 +0x388
main.main()
	/Users/<USER>/Desktop/Aniket-precize/precize-provider.nosync/precize-provider/enhancer/main.go:54 +0x390
2025/09/11 17:14:30 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 17:14:30 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 17:14:30 Index iac_git_commits exists - 
2025/09/11 17:14:30 Index cfstack_templates exists - 
2025/09/11 17:14:30 Index arm_templates exists - 
2025/09/11 17:14:30 Index terraform_resources exists - 
2025/09/11 17:14:30 Index tf_commits exists - 
2025/09/11 17:14:30 Index tf_variables exists - 
2025/09/11 17:14:30 Index resource_context exists - 
2025/09/11 17:14:30 Index text_lookup exists - 
2025/09/11 17:14:30 Index ai_resources exists - 
2025/09/11 17:14:30 Index idp_events exists - 
2025/09/11 17:14:30 Index idp_users exists - 
2025/09/11 17:14:30 Index idp_apps exists - 
2025/09/11 17:14:30 Index idp_groups exists - 
2025/09/11 17:14:31 Index cloud_incidents exists - 
2025/09/11 17:14:31 Index jira_issues exists - 
2025/09/11 17:14:31 Index jira_data exists - 
2025/09/11 17:14:31 Index jira_resources exists - 
2025/09/11 17:14:31 Index precize_creations exists - 
2025/09/11 17:14:31 Index external_cloud_resources exists - 
2025/09/11 17:14:31 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 17:14:31 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 17:14:35 Previous collected at - 1757422178899 - 
2025/09/11 17:14:35 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 17:14:35 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:14:36 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/09/11 17:14:38 Email Status Request - map[<EMAIL>:Precize_fa89e] - 
2025/09/11 17:14:38 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:38 Email Status Request - map[<EMAIL>:Test] - 
2025/09/11 17:14:38 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:39 Email Status Request - map[<EMAIL>:Muskaantest] - 
2025/09/11 17:14:39 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:40 Email Status Request - map[<EMAIL>:Abhay] - 
2025/09/11 17:14:42 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 17:14:42 Email Status Request - map[<EMAIL>:Testuser2] - 
2025/09/11 17:14:42 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:42 Email Status Request - map[<EMAIL>:Testdelete-Abhi] - 
2025/09/11 17:14:42 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:43 Email Status Request - map[<EMAIL>:Mathan] - 
2025/09/11 17:14:43 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 17:14:43 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:14:43 Email Status Request - map[<EMAIL>:Root] - 
2025/09/11 17:14:43 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:43 Email Status Request - map[<EMAIL>:Abhi-Terraform-Test] - 
2025/09/11 17:14:43 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:44 Email Status Request - map[<EMAIL>:Davidk] - 
2025/09/11 17:14:44 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 17:14:44 Email Status Request - map[<EMAIL>:John-Without-Console] - 
2025/09/11 17:14:44 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:45 Email Status Request - map[<EMAIL>:Sowmya] - 
2025/09/11 17:14:45 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 17:14:46 Email Status Request - map[<EMAIL>:Sai Ashish Das] - 
2025/09/11 17:14:46 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 17:14:46 Email Status Request - map[<EMAIL>:Arun] - 
2025/09/11 17:14:46 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:47 Email Status Request - map[<EMAIL>:Ops-Without-Console] - 
2025/09/11 17:14:47 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:47 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:14:48 Email Status Request - map[<EMAIL>:Pbatta] - 
2025/09/11 17:14:48 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:48 Email Status Request - map[<EMAIL>:Aniket] - 
2025/09/11 17:14:49 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 17:14:50 Email Status Request - map[<EMAIL>:Soc2-Audit] - 
2025/09/11 17:14:50 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:50 Email Status Request - map[<EMAIL>:Muskaan] - 
2025/09/11 17:14:50 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:50 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:14:52 Email Status Request - map[<EMAIL>:Engineering-With-Console] - 
2025/09/11 17:14:53 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 17:14:54 Email Status Request - map[<EMAIL>:Aarya] - 
2025/09/11 17:14:54 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:54 Email Status Request - map[<EMAIL>:Testuserforscan] - 
2025/09/11 17:14:54 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:54 Email Status Request - map[<EMAIL>:Tf-User-2] - 
2025/09/11 17:14:54 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:55 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:14:55 Email Status Request - map[<EMAIL>:Johnd] - 
2025/09/11 17:14:55 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 17:14:57 Email Status Request - map[<EMAIL>:Emergency-User] - 
2025/09/11 17:14:58 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:14:58 Email Status Request - map[<EMAIL>:Abhishek] - 
2025/09/11 17:14:59 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 17:15:00 Email Status Request - map[<EMAIL>:Abhishektest] - 
2025/09/11 17:15:00 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:00 Email Status Request - map[<EMAIL>:Precizeapplicationuser] - 
2025/09/11 17:15:01 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:01 Email Status Request - map[<EMAIL>:Test123] - 
2025/09/11 17:15:01 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:01 Email Status Request - map[<EMAIL>:Saiashish] - 
2025/09/11 17:15:02 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 17:15:02 Email Status Request - map[<EMAIL>:Swathi] - 
2025/09/11 17:15:03 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:03 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:15:04 Email Status Request - map[<EMAIL>:Okta-Test-Qa] - 
2025/09/11 17:15:04 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:05 Email Status Request - map[<EMAIL>:Testscan] - 
2025/09/11 17:15:05 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:06 Email Status Request - map[<EMAIL>:Aniket Dinda] - 
2025/09/11 17:15:06 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:08 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:15:09 Email Status Request - map[<EMAIL>:Precizeappforqa] - 
2025/09/11 17:15:10 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:10 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:15:11 Email Status Request - map[<EMAIL>:Vishalycustodianuser] - 
2025/09/11 17:15:11 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:11 Email Status Request - map[<EMAIL>:Iamuser-Abhi-Terraform] - 
2025/09/11 17:15:11 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:13 Email Status Request - map[<EMAIL>:Qadeploygithub] - 
2025/09/11 17:15:13 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:14 Email Status Request - map[<EMAIL>:Main-Server-User] - 
2025/09/11 17:15:14 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:14 Email Status Request - map[<EMAIL>:Power] - 
2025/09/11 17:15:14 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:16 Email Status Request - map[<EMAIL>:Precizeapp] - 
2025/09/11 17:15:16 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:19 Email Status Request - map[<EMAIL>:Satyam-With-Console] - 
2025/09/11 17:15:19 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 17:15:22 Email Status Request - map[<EMAIL>:Pavithra Esakiraj] - 
2025/09/11 17:15:23 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 17:15:26 Email Status Request - map[<EMAIL>:Eks] - 
2025/09/11 17:15:26 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:27 Email Status Request - map[<EMAIL>:Private-Lambda-Url-Function] - 
2025/09/11 17:15:27 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:28 Email Status Request - map[<EMAIL>:Automation_master_user] - 
2025/09/11 17:15:28 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:28 Email Status Request - map[<EMAIL>:Sowmyac] - 
2025/09/11 17:15:28 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 17:15:29 Email Status Request - map[<EMAIL>:Bedrock_iam_user] - 
2025/09/11 17:15:29 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:15:30 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Arun <EMAIL>:Megha Gowda muskaannitp14_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:Muskaannitp14 <EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Vishwas Manral] - 
2025/09/11 17:15:36 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable muskaannitp14_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:deliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable] - 
2025/09/11 17:15:36 Email Status Request - map[<EMAIL>:Abhishek G04 <EMAIL>:Aniket Dinda aniketdbpc_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:<NAME_EMAIL>:Johnd <EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Sai <NAME_EMAIL>:Sruthakeerthi <EMAIL>:Role-By-Qa <EMAIL>:Vishwas Manral] - 
2025/09/11 17:15:39 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:deliverable aniketdbpc_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:deliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:deliverable] - 
2025/09/11 17:15:39 Email Status Request - map[<EMAIL>:Abhayanoop <EMAIL>:Abhishekabhi1607 <EMAIL>:<NAME_EMAIL>:Mathan-With-Console <EMAIL>:Mathan <EMAIL>:Prasad Batta prbatta_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:Prasad Batta rohitsharma_infosys.com#ext#@vishwasmanralgmail.onmicrosoft.com:<NAME_EMAIL>:Satyam-With-Console <EMAIL>:Team Gowda] - 
2025/09/11 17:15:41 Email Status Response - map[<EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:undeliverable prbatta_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:deliverable rohitsharma_infosys.com#ext#@vishwasmanralgmail.onmicrosoft.com:deliverable <EMAIL>:undeliverable <EMAIL>:undeliverable] - 
2025/09/11 17:15:41 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Meghacy <EMAIL>:Muskaannitp14 <EMAIL>:<NAME_EMAIL>:Prbatta <EMAIL>:<NAME_EMAIL>:Abhay <NAME_EMAIL>:Test vishwas.manral_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:Vishwas Manral] - 
2025/09/11 17:15:44 Email Status Response - map[<EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:undeliverable vishwas.manral_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:deliverable] - 
2025/09/11 17:15:44 Email Status Request - map[<EMAIL>:<NAME_EMAIL>:Dummy User gangadhara.krishnappa_oracle.com#ext#@vishwasmanralgmail.onmicrosoft.com:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Rohit Sharma saiashish_precize.ai#ext#@vishwasmanralgmail.onmicrosoft.com:Sai <NAME_EMAIL>:<NAME_EMAIL>:Testing] - 
2025/09/11 17:15:45 Email Status Response - map[<EMAIL>:undeliverable <EMAIL>:undeliverable gangadhara.krishnappa_oracle.com#ext#@vishwasmanralgmail.onmicrosoft.com:undeliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable saiashish_precize.ai#ext#@vishwasmanralgmail.onmicrosoft.com:undeliverable <EMAIL>:undeliverable <EMAIL>:undeliverable] - 
2025/09/11 17:15:45 Email Status Request - map[<EMAIL>:Abhay <EMAIL>:Alain <EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Sai <NAME_EMAIL>:<NAME_EMAIL>:Sowmyac <EMAIL>:Sowmyachandrappa <EMAIL>:Venky Koppala] - 
2025/09/11 17:15:49 Email Status Response - map[<EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:deliverable <EMAIL>:undeliverable <EMAIL>:deliverable <EMAIL>:deliverable] - 
2025/09/11 17:15:49 Email Status Request - map[<EMAIL>:Aniket Dinda meghacy_gmail.com#ext#@vishwasmanralgmail.onmicrosoft.com:Megha <EMAIL>:<NAME_EMAIL>:<NAME_EMAIL>:Yaswanth Reddy] - 
2025/09/11 17:16:08 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 17:16:08 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 17:16:08 Index iac_git_commits exists - 
2025/09/11 17:16:09 Index cfstack_templates exists - 
2025/09/11 17:16:09 Index arm_templates exists - 
2025/09/11 17:16:09 Index terraform_resources exists - 
2025/09/11 17:16:09 Index tf_commits exists - 
2025/09/11 17:16:09 Index tf_variables exists - 
2025/09/11 17:16:09 Index resource_context exists - 
2025/09/11 17:16:09 Index text_lookup exists - 
2025/09/11 17:16:09 Index ai_resources exists - 
2025/09/11 17:16:09 Index idp_events exists - 
2025/09/11 17:16:09 Index idp_users exists - 
2025/09/11 17:16:09 Index idp_apps exists - 
2025/09/11 17:16:09 Index idp_groups exists - 
2025/09/11 17:16:09 Index cloud_incidents exists - 
2025/09/11 17:16:09 Index jira_issues exists - 
2025/09/11 17:16:09 Index jira_data exists - 
2025/09/11 17:16:09 Index jira_resources exists - 
2025/09/11 17:16:09 Index precize_creations exists - 
2025/09/11 17:16:09 Index external_cloud_resources exists - 
2025/09/11 17:16:09 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 17:16:09 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 17:16:13 Previous collected at - 1757422178899 - 
2025/09/11 17:16:13 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 17:16:13 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 17:16:14 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/09/11 17:16:15 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/09/11 17:16:15 [49] Resources fetched - 1 - 
2025/09/11 17:16:22 [49] Email Status Request - map[<EMAIL>:Service] - 
2025/09/11 17:16:22 [49] Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:16:31 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 17:16:31 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 17:16:31 Index iac_git_commits exists - 
2025/09/11 17:16:31 Index cfstack_templates exists - 
2025/09/11 17:16:31 Index arm_templates exists - 
2025/09/11 17:16:31 Index terraform_resources exists - 
2025/09/11 17:16:32 Index tf_commits exists - 
2025/09/11 17:16:32 Index tf_variables exists - 
2025/09/11 17:16:32 Index resource_context exists - 
2025/09/11 17:16:32 Index text_lookup exists - 
2025/09/11 17:16:32 Index ai_resources exists - 
2025/09/11 17:16:32 Index idp_events exists - 
2025/09/11 17:16:32 Index idp_users exists - 
2025/09/11 17:16:32 Index idp_apps exists - 
2025/09/11 17:16:32 Index idp_groups exists - 
2025/09/11 17:16:32 Index cloud_incidents exists - 
2025/09/11 17:16:32 Index jira_issues exists - 
2025/09/11 17:16:32 Index jira_data exists - 
2025/09/11 17:16:32 Index jira_resources exists - 
2025/09/11 17:16:32 Index precize_creations exists - 
2025/09/11 17:16:32 Index external_cloud_resources exists - 
2025/09/11 17:16:32 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 17:16:32 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 17:16:36 Previous collected at - 1757422178899 - 
2025/09/11 17:16:36 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 17:16:36 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 17:16:37 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 17:16:37 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 17:16:37 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 17:16:37 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 17:16:37 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:16:37 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:16:38 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 17:16:38 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:16:38 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/09/11 17:16:38 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/09/11 17:16:38 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/09/11 17:16:38 [36] Resources fetched - 1 - 
2025/09/11 17:16:57 [36] Email Status Request - map[<EMAIL>:Service] - 
2025/09/11 17:16:57 [36] Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:30:05 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 17:30:06 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 17:30:06 Index iac_git_commits exists - 
2025/09/11 17:30:06 Index cfstack_templates exists - 
2025/09/11 17:30:06 Index arm_templates exists - 
2025/09/11 17:30:06 Index terraform_resources exists - 
2025/09/11 17:30:06 Index tf_commits exists - 
2025/09/11 17:30:06 Index tf_variables exists - 
2025/09/11 17:30:06 Index resource_context exists - 
2025/09/11 17:30:06 Index text_lookup exists - 
2025/09/11 17:30:06 Index ai_resources exists - 
2025/09/11 17:30:06 Index idp_events exists - 
2025/09/11 17:30:06 Index idp_users exists - 
2025/09/11 17:30:06 Index idp_apps exists - 
2025/09/11 17:30:06 Index idp_groups exists - 
2025/09/11 17:30:06 Index cloud_incidents exists - 
2025/09/11 17:30:06 Index jira_issues exists - 
2025/09/11 17:30:06 Index jira_data exists - 
2025/09/11 17:30:06 Index jira_resources exists - 
2025/09/11 17:30:06 Index precize_creations exists - 
2025/09/11 17:30:07 Index external_cloud_resources exists - 
2025/09/11 17:30:07 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 17:30:07 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 17:30:10 Previous collected at - 1757422178899 - 
2025/09/11 17:30:10 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 17:30:10 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 17:30:11 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 17:30:11 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/09/11 17:30:12 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/09/11 17:30:12 [65] Resources fetched - 1 - 
2025/09/11 17:30:17 [65] [0R8Da4gBoELr5xpoQ6Y3] OpenAI Request - lmNA3D - Given a list of text strings, analyze each string to determine if it contains a person's name by following these rules:
		1. Consider both Western and non-Western name formats.
		2. Exclude common words that might look like names but are actually:
		- Company names
		- Product names
		- Common nouns that happen to match name patterns.
		
		Return a JSON object where each input string is a key, and its corresponding value is a boolean (true if it contains a human name, false otherwise). 

		STRICTLY follow this format:
		{
		"name1": true,
		"name2": false,
		...
		}

		Ensure that the values are proper boolean types (not strings). Do NOT use quotes around 'true' or 'false'.

		Examples:
- John Doe: true
- Engineering: false
- Aniket: true
- DevOps: false

Inputs: "Service ************* Gcf Admin Robot Iam Gserviceaccount Com" - 
2025/09/11 17:30:17 [65] Token invalid - Empty token - 
2025/09/11 17:30:17 [65] Email Status Request - map[<EMAIL>:Service ************* Gcf Admin Robot Iam Gserviceaccount Com] - 
2025/09/11 17:30:17 [65] Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 17:59:10 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 17:59:10 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 17:59:11 Index iac_git_commits exists - 
2025/09/11 17:59:11 Index cfstack_templates exists - 
2025/09/11 17:59:11 Index arm_templates exists - 
2025/09/11 17:59:11 Index terraform_resources exists - 
2025/09/11 17:59:11 Index tf_commits exists - 
2025/09/11 17:59:11 Index tf_variables exists - 
2025/09/11 17:59:11 Index resource_context exists - 
2025/09/11 17:59:11 Index text_lookup exists - 
2025/09/11 17:59:11 Index ai_resources exists - 
2025/09/11 17:59:11 Index idp_events exists - 
2025/09/11 17:59:11 Index idp_users exists - 
2025/09/11 17:59:11 Index idp_apps exists - 
2025/09/11 17:59:11 Index idp_groups exists - 
2025/09/11 17:59:11 Index cloud_incidents exists - 
2025/09/11 17:59:11 Index jira_issues exists - 
2025/09/11 17:59:11 Index jira_data exists - 
2025/09/11 17:59:11 Index jira_resources exists - 
2025/09/11 17:59:11 Index precize_creations exists - 
2025/09/11 17:59:12 Index external_cloud_resources exists - 
2025/09/11 17:59:12 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 17:59:12 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 17:59:15 Previous collected at - 1757422178899 - 
2025/09/11 17:59:15 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 17:59:15 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 17:59:16 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 17:59:16 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 17:59:16 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 17:59:16 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 17:59:16 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:59:17 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 17:59:17 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 17:59:17 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 17:59:17 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/09/11 17:59:17 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/09/11 17:59:17 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/09/11 17:59:17 [66] Resources fetched - 1 - 
2025/09/11 17:59:33 [66] Email Status Request - map[<EMAIL>:Service] - 
2025/09/11 17:59:33 [66] Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:01:18 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/09/11 18:01:18 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/09/11 18:01:18 <EMAIL> - UserContext:  Name: Service  Email: <EMAIL>  Active: false  IsUser: false  IsUserEvaluated: true  IsInvalid: true  IsSddl: false  SkipUser: false  ActiveAccounts: *************: 2  - 
2025/09/11 18:01:18 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 3000 - ************* - 
2025/09/11 18:01:18 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 3000 - ************* - 
2025/09/11 18:01:18 [0R8Da4gBoELr5xpoQ6Y3] Completed post process stage 1 - 3000 - ************* - 
2025/09/11 18:01:23 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 2 - 3000 - ************* - 
2025/09/11 18:08:00 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 18:08:00 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 18:08:01 Index iac_git_commits exists - 
2025/09/11 18:08:01 Index cfstack_templates exists - 
2025/09/11 18:08:01 Index arm_templates exists - 
2025/09/11 18:08:01 Index terraform_resources exists - 
2025/09/11 18:08:01 Index tf_commits exists - 
2025/09/11 18:08:01 Index tf_variables exists - 
2025/09/11 18:08:01 Index resource_context exists - 
2025/09/11 18:08:01 Index text_lookup exists - 
2025/09/11 18:08:01 Index ai_resources exists - 
2025/09/11 18:08:01 Index idp_events exists - 
2025/09/11 18:08:01 Index idp_users exists - 
2025/09/11 18:08:01 Index idp_apps exists - 
2025/09/11 18:08:01 Index idp_groups exists - 
2025/09/11 18:08:01 Index cloud_incidents exists - 
2025/09/11 18:08:01 Index jira_issues exists - 
2025/09/11 18:08:01 Index jira_data exists - 
2025/09/11 18:08:01 Index jira_resources exists - 
2025/09/11 18:08:01 Index precize_creations exists - 
2025/09/11 18:08:01 Index external_cloud_resources exists - 
2025/09/11 18:08:01 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 18:08:01 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 18:08:04 Previous collected at - 1757422178899 - 
2025/09/11 18:08:04 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 18:08:04 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 18:08:05 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 18:08:05 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 18:08:05 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 18:08:05 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 18:08:05 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:08:05 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:08:05 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 18:08:05 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:08:06 [0R8Da4gBoELr5xpoQ6Y3] Gathering cloud users - 
2025/09/11 18:08:07 Email Status Request - map[<EMAIL>:Root] - 
2025/09/11 18:08:07 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:07 Email Status Request - map[<EMAIL>:Precize_656ab] - 
2025/09/11 18:08:07 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:07 Email Status Request - map[<EMAIL>:Test_bedrock_models] - 
2025/09/11 18:08:07 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:07 Email Status Request - map[<EMAIL>:Soc2-Audit] - 
2025/09/11 18:08:08 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:08 Email Status Request - map[<EMAIL>:Sai Ashish Das] - 
2025/09/11 18:08:08 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 18:08:08 Email Status Request - map[<EMAIL>:Qadeploygithub] - 
2025/09/11 18:08:08 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:08 Email Status Request - map[<EMAIL>:Abhishektest] - 
2025/09/11 18:08:09 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:09 Email Status Request - map[<EMAIL>:Sowmya  Gowda] - 
2025/09/11 18:08:09 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 18:08:10 Email Status Request - map[<EMAIL>:Test123] - 
2025/09/11 18:08:10 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:10 Email Status Request - map[<EMAIL>:Vishalycustodianuser] - 
2025/09/11 18:08:10 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:12 Email Status Request - map[<EMAIL>:Precizeapplicationuser] - 
2025/09/11 18:08:12 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:12 Email Status Request - map[<EMAIL>:Mathan-With-Console] - 
2025/09/11 18:08:12 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 18:08:12 Email Status Request - map[<EMAIL>:Johnd] - 
2025/09/11 18:08:12 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 18:08:13 Email Status Request - map[<EMAIL>:Muskaan] - 
2025/09/11 18:08:13 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:14 Email Status Request - map[<EMAIL>:Automation_master_user] - 
2025/09/11 18:08:14 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:15 Email Status Request - map[<EMAIL>:Aarya] - 
2025/09/11 18:08:15 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:15 Email Status Request - map[<EMAIL>:Satyam-With-Console] - 
2025/09/11 18:08:15 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 18:08:16 Email Status Request - map[<EMAIL>:Testdelete-Abhi] - 
2025/09/11 18:08:16 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:17 Email Status Request - map[<EMAIL>:Abhay Anoop] - 
2025/09/11 18:08:18 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 18:08:18 Email Status Request - map[<EMAIL>:Power] - 
2025/09/11 18:08:18 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:19 Email Status Request - map[<EMAIL>:Swathi] - 
2025/09/11 18:08:19 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:20 Email Status Request - map[<EMAIL>:Tf-User-1] - 
2025/09/11 18:08:20 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:21 Email Status Request - map[<EMAIL>:Emergency-User] - 
2025/09/11 18:08:21 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:21 Email Status Request - map[<EMAIL>:Main-Server-User] - 
2025/09/11 18:08:21 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:24 Email Status Request - map[<EMAIL>:Testuserforscan] - 
2025/09/11 18:08:24 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:26 Email Status Request - map[<EMAIL>:Engineering-Without-Console] - 
2025/09/11 18:08:27 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 18:08:28 Email Status Request - map[<EMAIL>:Sowmyac] - 
2025/09/11 18:08:28 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 18:08:29 Email Status Request - map[<EMAIL>:Aniket Dinda] - 
2025/09/11 18:08:29 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:29 Email Status Request - map[<EMAIL>:Aniket Dinda] - 
2025/09/11 18:08:30 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 18:08:30 Email Status Request - map[<EMAIL>:Precizeappforqa] - 
2025/09/11 18:08:30 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:33 Email Status Request - map[<EMAIL>:Pavithra Esakiraj] - 
2025/09/11 18:08:34 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 18:08:34 Email Status Request - map[<EMAIL>:Eks] - 
2025/09/11 18:08:34 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:35 Email Status Request - map[<EMAIL>:John-With-Console] - 
2025/09/11 18:08:35 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:36 Email Status Request - map[<EMAIL>:Arun] - 
2025/09/11 18:08:36 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:36 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:08:37 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:08:38 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:08:38 Email Status Request - map[<EMAIL>:Abhi-Terraform-Test] - 
2025/09/11 18:08:38 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:38 Email Status Request - map[<EMAIL>:Muskaantest] - 
2025/09/11 18:08:38 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:38 Email Status Request - map[<EMAIL>:Okta-Test-Qa] - 
2025/09/11 18:08:38 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:39 Email Status Request - map[<EMAIL>:Private-Lambda-Url-Function] - 
2025/09/11 18:08:39 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:40 Email Status Request - map[<EMAIL>:Testscan] - 
2025/09/11 18:08:40 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:40 Email Status Request - map[<EMAIL>:Iamuser-Abhi-Terraform] - 
2025/09/11 18:08:40 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:41 Email Status Request - map[<EMAIL>:Ops-Without-Console] - 
2025/09/11 18:08:41 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:42 Email Status Request - map[<EMAIL>:Saiashish] - 
2025/09/11 18:08:43 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 18:08:43 Email Status Request - map[<EMAIL>:Precizeapp] - 
2025/09/11 18:08:43 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:44 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:08:45 Email Status Request - map[<EMAIL>:Testuser2] - 
2025/09/11 18:08:45 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:08:46 Email Status Request - map[<EMAIL>:Abhishek] - 
2025/09/11 18:08:46 Email Status Response - map[<EMAIL>:deliverable] - 
2025/09/11 18:08:49 Email Status Request - map[<EMAIL>:Davidk] - 
2025/09/11 18:08:49 Email Status Response - map[<EMAIL>:undeliverable] - 
2025/09/11 18:08:52 Email Status Request - map[<EMAIL>:Pbatta] - 
2025/09/11 18:08:52 Email Status Response - map[<EMAIL>:invalid] - 
2025/09/11 18:09:02 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 18:09:02 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 18:09:02 Index iac_git_commits exists - 
2025/09/11 18:09:02 Index cfstack_templates exists - 
2025/09/11 18:09:02 Index arm_templates exists - 
2025/09/11 18:09:02 Index terraform_resources exists - 
2025/09/11 18:09:03 Index tf_commits exists - 
2025/09/11 18:09:03 Index tf_variables exists - 
2025/09/11 18:09:03 Index resource_context exists - 
2025/09/11 18:09:03 Index text_lookup exists - 
2025/09/11 18:09:03 Index ai_resources exists - 
2025/09/11 18:09:03 Index idp_events exists - 
2025/09/11 18:09:03 Index idp_users exists - 
2025/09/11 18:09:03 Index idp_apps exists - 
2025/09/11 18:09:03 Index idp_groups exists - 
2025/09/11 18:09:03 Index cloud_incidents exists - 
2025/09/11 18:09:03 Index jira_issues exists - 
2025/09/11 18:09:03 Index jira_data exists - 
2025/09/11 18:09:03 Index jira_resources exists - 
2025/09/11 18:09:03 Index precize_creations exists - 
2025/09/11 18:09:03 Index external_cloud_resources exists - 
2025/09/11 18:09:03 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 18:09:03 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 18:09:07 Previous collected at - 1757422178899 - 
2025/09/11 18:09:08 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 18:09:08 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/09/11 18:09:09 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/09/11 18:09:09 [35] Resources fetched - 1 - 
2025/09/11 18:10:16 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/09/11 18:10:16 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/09/11 18:10:16 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 3000 - ************* - 
2025/09/11 18:10:16 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 3000 - ************* - 
2025/09/11 18:10:37 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 18:10:37 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 18:10:37 Index iac_git_commits exists - 
2025/09/11 18:10:37 Index cfstack_templates exists - 
2025/09/11 18:10:37 Index arm_templates exists - 
2025/09/11 18:10:37 Index terraform_resources exists - 
2025/09/11 18:10:37 Index tf_commits exists - 
2025/09/11 18:10:37 Index tf_variables exists - 
2025/09/11 18:10:37 Index resource_context exists - 
2025/09/11 18:10:37 Index text_lookup exists - 
2025/09/11 18:10:37 Index ai_resources exists - 
2025/09/11 18:10:38 Index idp_events exists - 
2025/09/11 18:10:38 Index idp_users exists - 
2025/09/11 18:10:38 Index idp_apps exists - 
2025/09/11 18:10:38 Index idp_groups exists - 
2025/09/11 18:10:38 Index cloud_incidents exists - 
2025/09/11 18:10:38 Index jira_issues exists - 
2025/09/11 18:10:38 Index jira_data exists - 
2025/09/11 18:10:38 Index jira_resources exists - 
2025/09/11 18:10:38 Index precize_creations exists - 
2025/09/11 18:10:38 Index external_cloud_resources exists - 
2025/09/11 18:10:38 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 18:10:38 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 18:10:40 Previous collected at - 1757422178899 - 
2025/09/11 18:10:41 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 18:10:41 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/09/11 18:10:42 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/09/11 18:10:42 [26] Resources fetched - 1 - 
2025/09/11 18:10:48 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for resource context - 
2025/09/11 18:10:48 [0R8Da4gBoELr5xpoQ6Y3] User list for post processing - ************* - 
2025/09/11 18:10:48 [0R8Da4gBoELr5xpoQ6Y3] Starting post process - 3000 - ************* - 
2025/09/11 18:10:48 [0R8Da4gBoELr5xpoQ6Y3] Starting post process stage 1 - 3000 - ************* - 
2025/09/11 18:24:27 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 18:24:27 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 18:24:27 Index iac_git_commits exists - 
2025/09/11 18:24:27 Index cfstack_templates exists - 
2025/09/11 18:24:27 Index arm_templates exists - 
2025/09/11 18:24:27 Index terraform_resources exists - 
2025/09/11 18:24:27 Index tf_commits exists - 
2025/09/11 18:24:27 Index tf_variables exists - 
2025/09/11 18:24:27 Index resource_context exists - 
2025/09/11 18:24:27 Index text_lookup exists - 
2025/09/11 18:24:27 Index ai_resources exists - 
2025/09/11 18:24:27 Index idp_events exists - 
2025/09/11 18:24:27 Index idp_users exists - 
2025/09/11 18:24:28 Index idp_apps exists - 
2025/09/11 18:24:28 Index idp_groups exists - 
2025/09/11 18:24:28 Index cloud_incidents exists - 
2025/09/11 18:24:28 Index jira_issues exists - 
2025/09/11 18:24:28 Index jira_data exists - 
2025/09/11 18:24:28 Index jira_resources exists - 
2025/09/11 18:24:28 Index precize_creations exists - 
2025/09/11 18:24:28 Index external_cloud_resources exists - 
2025/09/11 18:24:28 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 18:24:28 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 18:24:31 Previous collected at - 1757422178899 - 
2025/09/11 18:24:32 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 18:24:32 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/09/11 18:24:33 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/09/11 18:24:33 [49] Resources fetched - 1 - 
2025/09/11 18:25:36 Application config could not be read. Starting with defaults - application.yml - 
2025/09/11 18:25:37 Connected to Elasticsearch - [200 OK] {
  "name" : "es-master-01",
  "cluster_name" : "preprod-es-cluster",
  "cluster_uuid" : "aUogMtiJQV-NB5xY5m64Aw",
  "version" : {
    "number" : "7.6.2",
    "build_flavor" : "default",
    "build_type" : "tar",
    "build_hash" : "ef48eb35cf30adf4db14086e8aabd07ef6fb113f",
    "build_date" : "2020-03-26T06:34:37.794943Z",
    "build_snapshot" : false,
    "lucene_version" : "8.4.0",
    "minimum_wire_compatibility_version" : "6.8.0",
    "minimum_index_compatibility_version" : "6.0.0-beta1"
  },
  "tagline" : "You Know, for Search"
}
 - 
2025/09/11 18:25:37 Index iac_git_commits exists - 
2025/09/11 18:25:37 Index cfstack_templates exists - 
2025/09/11 18:25:37 Index arm_templates exists - 
2025/09/11 18:25:37 Index terraform_resources exists - 
2025/09/11 18:25:37 Index tf_commits exists - 
2025/09/11 18:25:37 Index tf_variables exists - 
2025/09/11 18:25:37 Index resource_context exists - 
2025/09/11 18:25:37 Index text_lookup exists - 
2025/09/11 18:25:37 Index ai_resources exists - 
2025/09/11 18:25:37 Index idp_events exists - 
2025/09/11 18:25:37 Index idp_users exists - 
2025/09/11 18:25:37 Index idp_apps exists - 
2025/09/11 18:25:37 Index idp_groups exists - 
2025/09/11 18:25:37 Index cloud_incidents exists - 
2025/09/11 18:25:37 Index jira_issues exists - 
2025/09/11 18:25:37 Index jira_data exists - 
2025/09/11 18:25:38 Index jira_resources exists - 
2025/09/11 18:25:38 Index precize_creations exists - 
2025/09/11 18:25:38 Index external_cloud_resources exists - 
2025/09/11 18:25:38 [0R8Da4gBoELr5xpoQ6Y3] Processing context data for tenant - 3000 - ************* - 
2025/09/11 18:25:38 Calling cred api for - 0R8Da4gBoELr5xpoQ6Y3 - 
2025/09/11 18:25:41 Previous collected at - 1757422178899 - 
2025/09/11 18:25:41 [0R8Da4gBoELr5xpoQ6Y3] Global App Context for tenant - [Precize-integration Web.precize.ai Qa.precize.ai Precize-Terraform-Context QA Mysecondapp Dev Activity Weather Prod Test Precize Redis] - 
2025/09/11 18:25:41 [0R8Da4gBoELr5xpoQ6Y3] Global Team Context for tenant - [QA QA-Automation Precize QA-rangers QA-API-Automation Manual Testing QA_Precize QAtestchanged Ind team QAtest US Precize-QA-Auto Devops-India Precize-Amulya Random Amulya Precize-Qa Platformdev Providerdev Infradevcore Test] - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Owner exclusion exceptions - [map[_id:db2482e4afb0970dc0e1 key:aniket op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[aniketing]] map[_id:Eiu-uJQBhDrCSK6oJ5SR key:user op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:owner_match values:[user]]] - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Owner inclusion exceptions - [] - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Typo exceptions - [map[_id:IThJZ5UB6JOphredKu0x key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:typo values:[<EMAIL>]]] - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Owner email name match exceptions - [] - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Derived Email exclusions - [map[_id:798FDpIBcqa1_4puN6e4 key:abhay anoop op:ne tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Derived Email inclusions - [map[_id:7t8EDpIBcqa1_4pu26fV key:aniket op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:email_derivation values:[<EMAIL>]]] - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Parent Child inclusion exceptions - [map[_id:lYwztJYB0y00wVulGaLa key:<EMAIL> op:eq tenantId:0R8Da4gBoELr5xpoQ6Y3 type:parent_child_email values:[<EMAIL>]]] - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Not overwriting child primary email as proxy exists - <EMAIL> - <EMAIL> - <EMAIL> - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Processing started for jira context - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Processing complete for jira context - 
2025/09/11 18:25:43 [0R8Da4gBoELr5xpoQ6Y3] Processing started for resource context - 
2025/09/11 18:25:43 [26] Resources fetched - 1 - 
2025/09/11 18:25:57 [26] Email Status Request - map[<EMAIL>:Service] - 
2025/09/11 18:25:57 [26] Email Status Response - map[<EMAIL>:invalid] - 
