# Resource Context Categorization Service

This service categorizes resource context records into four distinct categories based on the relationship between customer-defined context and system-derived context.

## Categories

The service categorizes each resource into one or more of the following categories:

### 1. Enhanced Existing Context (`enhanced_existing_context`)
- **Description**: We added some extra context (customer had some existing context)
- **Criteria**: Resource has both customer-defined context AND system-derived context
- **Example**: Customer defined an owner via tags, and we also derived additional owners from IAM policies

### 2. All Derived Context (`all_derived_context`)
- **Description**: All context derived by us
- **Criteria**: Resource has only system-derived context, no customer-defined context
- **Example**: We derived owners, environment, and applications entirely from system analysis

### 3. Contradictory Context (`contradictory_context`)
- **Description**: What was there, and what we derive are contradictory
- **Criteria**: Customer-defined context conflicts with system-derived context
- **Example**: Customer tagged resource as "Production" but we derived it as "Development" based on naming patterns

### 4. No Derived Context (`no_derived_context`)
- **Description**: We could not derive context
- **Criteria**: Resource has no customer-defined context AND no system-derived context
- **Example**: Resource with no tags and no derivable patterns

## Customer-Defined Context Sources

Customer context is identified from two sources:

1. **Tags**: Resource tags that define context (owner, environment, application, etc.)
2. **Customer Entity Context**: Context added via the `cust_entity_context` API

### Exclusions
Context is NOT considered customer-defined if:
- `updatedBy` field is `<EMAIL>`
- `updatedBy` field starts with `support_`

## Usage

### Basic Usage

```go
import "github.com/precize/enhancer/context"

// Create the categorization service
service := context.NewResourceContextCategorizationService(tenantID, collectedAt, serviceID)

// Run categorization for all resources
err := service.CategorizeResourceContexts()
if err != nil {
    // Handle error
}
```

### Convenience Function

```go
import "github.com/precize/enhancer/context"

// Categorize resources for a specific tenant and collection
err := context.CategorizeResourceContextsForTenant("tenant-123", "1640995200000", 1000)
if err != nil {
    // Handle error
}
```

## Parameters

- **tenantID**: The tenant identifier
- **collectedAt**: Timestamp of the collection (string format)
- **serviceID**: Service identifier (e.g., 1000 for AWS, 2000 for Azure, 3000 for GCP)

## Database Updates

The service updates the `resource_context` index in Elasticsearch, adding a `contextCategories` field to each document containing an array of applicable category strings.

## Implementation Details

### Context Type Detection

The service checks for customer-defined context in the following fields:
- `DefinedOwners`, `DefinedEnv`, `DefinedApp`, `DefinedSoftware`
- `DefinedDeployment`, `DefinedSensitivity`, `DefinedCompliance`
- `DefinedCostCenter`, `DefinedTeam`, `DefinedTTL`

### Derived Context Detection

The service checks for system-derived context in:
- `DerivedOwners`, `DerivedEnv`, `DerivedApp`, `DerivedSoftware`
- `DerivedDeployment`, `DerivedSensitivity`, `DerivedCompliance`
- `DerivedTeam`, `DerivedTTL`

### Contradiction Detection

Contradictions are detected by comparing the names of context items between customer-defined and derived categories. If there's no overlap in names between customer and derived context of the same type, it's considered contradictory.

## Testing

Run the tests to verify functionality:

```bash
cd enhancer/context
go test -v -run TestResourceContextCategorization
```

## Example Output

After running the categorization service, resource context documents will have a new field:

```json
{
  "id": "resource-123",
  "resourceId": "i-1234567890abcdef0",
  "resourceType": "AWS::EC2::Instance",
  "contextCategories": [
    "enhanced_existing_context",
    "contradictory_context"
  ],
  // ... other fields
}
```

## Error Handling

The service includes comprehensive error handling for:
- Elasticsearch query failures
- Document parsing errors
- Bulk update failures

All errors are logged with appropriate context for debugging.
