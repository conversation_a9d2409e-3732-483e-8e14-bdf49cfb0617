package context

import (
	"strconv"

	"github.com/precize/logger"
)

// ExampleUsage demonstrates how to use the ResourceContextCategorizationService
func ExampleUsage() {
	// Example parameters - these would typically come from your application
	tenantID := "example-tenant-123"
	collectedAt := "1640995200000" // Unix timestamp in milliseconds
	serviceID := 1000              // AWS service ID

	// Create the categorization service
	service := NewResourceContextCategorizationService(tenantID, collectedAt, serviceID)

	// Run the categorization process
	if err := service.CategorizeResourceContexts(); err != nil {
		logger.Print(logger.ERROR, "Failed to categorize resource contexts", []string{err.Error()})
		return
	}

	logger.Print(logger.INFO, "Resource context categorization completed successfully", []string{tenantID})
}

// CategorizeResourceContextsForTenant is a convenience function that can be called from other parts of the application
func CategorizeResourceContextsForTenant(tenantID, collectedAt string, serviceID int) error {
	logger.Print(logger.INFO, "Starting resource context categorization for tenant", []string{tenantID, collectedAt, strconv.Itoa(serviceID)})

	service := NewResourceContextCategorizationService(tenantID, collectedAt, serviceID)
	return service.CategorizeResourceContexts()
}
