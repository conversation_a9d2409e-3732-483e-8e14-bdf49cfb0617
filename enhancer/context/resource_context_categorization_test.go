package context

import (
	"testing"

	"github.com/precize/common"
)

func TestResourceContextCategorization(t *testing.T) {
	service := &ResourceContextCategorizationService{
		TenantID:    "test-tenant",
		CollectedAt: "1640995200000",
		ServiceID:   1000,
	}

	// Test case 1: Enhanced existing context (customer has context + we derived context)
	t.Run("Enhanced Existing Context", func(t *testing.T) {
		doc := common.ResourceContextInsertDoc{
			ResourceOwnerTypes: common.ResourceOwnerTypes{
				DefinedOwners: []common.ResourceContextItem{
					{
						Name: "customer-owner",
						Type: common.CUSTOMER_DEFINED_USER_TYPE,
						Desc: "Customer <NAME_EMAIL>",
					},
				},
				DerivedOwners: []common.ResourceContextItem{
					{
						Name: "derived-owner",
						Type: common.RESOURCE_OWNER_USER_TYPE,
					},
				},
			},
		}

		categories := service.categorizeResource(doc)
		if len(categories) == 0 {
			t.Error("Expected categories but got none")
		}

		found := false
		for _, category := range categories {
			if category == common.ENHANCED_EXISTING_CONTEXT {
				found = true
				break
			}
		}
		if !found {
			t.Error("Expected ENHANCED_EXISTING_CONTEXT category")
		}
	})

	// Test case 2: All derived context (no customer context, only derived)
	t.Run("All Derived Context", func(t *testing.T) {
		doc := common.ResourceContextInsertDoc{
			ResourceOwnerTypes: common.ResourceOwnerTypes{
				DerivedOwners: []common.ResourceContextItem{
					{
						Name: "derived-owner",
						Type: common.RESOURCE_OWNER_USER_TYPE,
					},
				},
			},
		}

		categories := service.categorizeResource(doc)
		if len(categories) == 0 {
			t.Error("Expected categories but got none")
		}

		found := false
		for _, category := range categories {
			if category == common.ALL_DERIVED_CONTEXT {
				found = true
				break
			}
		}
		if !found {
			t.Error("Expected ALL_DERIVED_CONTEXT category")
		}
	})

	// Test case 3: No derived context (no customer context, no derived context)
	t.Run("No Derived Context", func(t *testing.T) {
		doc := common.ResourceContextInsertDoc{
			ResourceOwnerTypes: common.ResourceOwnerTypes{},
		}

		categories := service.categorizeResource(doc)
		if len(categories) == 0 {
			t.Error("Expected categories but got none")
		}

		found := false
		for _, category := range categories {
			if category == common.NO_DERIVED_CONTEXT {
				found = true
				break
			}
		}
		if !found {
			t.Error("Expected NO_DERIVED_CONTEXT category")
		}
	})

	// Test case 4: Contradictory context (different owners in customer vs derived)
	t.Run("Contradictory Context", func(t *testing.T) {
		doc := common.ResourceContextInsertDoc{
			ResourceOwnerTypes: common.ResourceOwnerTypes{
				DefinedOwners: []common.ResourceContextItem{
					{
						Name: "customer-owner",
						Type: common.CUSTOMER_DEFINED_USER_TYPE,
						Desc: "Customer <NAME_EMAIL>",
					},
				},
				DerivedOwners: []common.ResourceContextItem{
					{
						Name: "different-derived-owner",
						Type: common.RESOURCE_OWNER_USER_TYPE,
					},
				},
			},
		}

		categories := service.categorizeResource(doc)
		if len(categories) == 0 {
			t.Error("Expected categories but got none")
		}

		found := false
		for _, category := range categories {
			if category == common.CONTRADICTORY_CONTEXT {
				found = true
				break
			}
		}
		if !found {
			t.Error("Expected CONTRADICTORY_CONTEXT category")
		}
	})
}

func TestIsCustomerDefined(t *testing.T) {
	service := &ResourceContextCategorizationService{}

	// Test customer-defined type
	item1 := common.ResourceContextItem{
		Type: common.CUSTOMER_DEFINED_USER_TYPE,
	}
	if !service.isCustomerDefined(item1) {
		t.Error("Expected customer-defined type to be identified as customer-defined")
	}

	// Test tag-based context
	item2 := common.ResourceContextItem{
		Type: "tag_owner",
	}
	if !service.isCustomerDefined(item2) {
		t.Error("Expected tag-based context to be identified as customer-defined")
	}

	// Test admin/support exclusion
	item3 := common.ResourceContextItem{
		Type: common.CUSTOMER_DEFINED_USER_TYPE,
		Desc: "Customer <NAME_EMAIL>",
	}
	if service.isCustomerDefined(item3) {
		t.Error("Expected <EMAIL> to be excluded from customer-defined")
	}

	item4 := common.ResourceContextItem{
		Type: common.CUSTOMER_DEFINED_USER_TYPE,
		Desc: "Customer Defined by support_user",
	}
	if service.isCustomerDefined(item4) {
		t.Error("Expected support_ user to be excluded from customer-defined")
	}

	// Test derived type (should not be customer-defined)
	item5 := common.ResourceContextItem{
		Type: common.RESOURCE_OWNER_USER_TYPE,
	}
	if service.isCustomerDefined(item5) {
		t.Error("Expected derived type to not be identified as customer-defined")
	}
}
