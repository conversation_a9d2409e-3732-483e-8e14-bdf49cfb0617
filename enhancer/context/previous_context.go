package context

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

func processPreviousContext(resourceContext *ResourceContext, contextDocIDs []string) {

	var (
		previousContextDocIDs []string
		prevToCurrentDocIDMap = make(map[string]string)
	)

	if len(resourceContext.PreviousCollectedAt) == 0 {
		return
	}

	for _, currentContextDocID := range contextDocIDs {

		if currentContextDoc, ok := resourceContext.GetResourceContextInsertDoc(currentContextDocID); ok {

			var previousContextDocID string

			switch currentContextDoc.ResourceType {
			case common.AWS_ACCOUNT_RESOURCE_TYPE, common.AWS_ORGUNIT_RESOURCE_TYPE, common.AWS_ORG_RESOURCE_TYPE, common.AZURE_SUBSCRIPTION_RESOURCE_TYPE, common.AZURE_MGMTGRP_RESOURCE_TYPE, common.AZURE_TENANT_RESOURCE_TYPE, common.GCP_PROJECT_RESOURCE_TYPE, common.GCP_FOLDER_RESOURCE_TYPE, common.GCP_ORG_RESOURCE_TYPE:

				previousContextDocID = common.GenerateCombinedHashID(currentContextDoc.ResourceID, currentContextDoc.ResourceType, currentContextDoc.ResourceID, resourceContext.PreviousCollectedAt, resourceContext.TenantID)

			default:

				previousContextDocID = common.GenerateCombinedHashID(currentContextDoc.ResourceID, currentContextDoc.ResourceType, currentContextDoc.Account, resourceContext.PreviousCollectedAt, resourceContext.TenantID)

			}

			if len(previousContextDocID) > 0 {
				previousContextDocIDs = append(previousContextDocIDs, previousContextDocID)
				prevToCurrentDocIDMap[previousContextDocID] = currentContextDocID
			}
		}
	}

	prevContextQuery := `{"query":{"ids":{"values":["` + strings.Join(previousContextDocIDs, `","`) + `"]}}}`

	prevContextDocs, err := elastic.ExecuteSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, prevContextQuery)
	if err != nil {
		return
	}

	for _, prevContextDoc := range prevContextDocs {

		prevContextDocBytes, err := json.Marshal(prevContextDoc)
		if err != nil {
			logger.Print(logger.ERROR, "Got error marshalling", []string{resourceContext.TenantID}, err)
			return
		}

		var previousContextDoc common.ResourceContextInsertDoc

		if err = json.Unmarshal(prevContextDocBytes, &previousContextDoc); err != nil {
			logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID}, err)
			return
		}

		currentContextDocID := prevToCurrentDocIDMap[previousContextDoc.ID]

		if currentContextDoc, ok := resourceContext.GetResourceContextInsertDoc(currentContextDocID); ok {
			processPreviousContextForOwners(resourceContext, &currentContextDoc, previousContextDoc)
			processPreviousContextForApplications(resourceContext, &currentContextDoc, previousContextDoc)
			processPreviousContextForDeployments(resourceContext, &currentContextDoc, previousContextDoc)
			processPreviousContextForUserAgents(&currentContextDoc, previousContextDoc)
			resourceContext.SetResourceContextInsertDoc(currentContextDocID, currentContextDoc)
		}
	}
}

func processPreviousContextForOwners(resourceContext *ResourceContext, currentContextDoc *common.ResourceContextInsertDoc, previousContextDoc common.ResourceContextInsertDoc) {

	activityUsers := make(map[string]common.ResourceContextItem)

	for i, prevDerivedOwner := range previousContextDoc.ResourceOwnerTypes.DerivedOwners {

		if prevDerivedOwner.Type == common.ACTIVITY_USER_TYPE || prevDerivedOwner.Type == common.CREATOR_USER_TYPE {

			if prevDerivedOwner.Event != nil && !prevDerivedOwner.Event.IndirectEvent {

				var present bool

				for _, currentDerivedOwner := range currentContextDoc.ResourceOwnerTypes.DerivedOwners {

					if currentDerivedOwner.Type == common.ACTIVITY_USER_TYPE || currentDerivedOwner.Type == common.CREATOR_USER_TYPE {

						if strings.ToLower(currentDerivedOwner.Name) == strings.ToLower(prevDerivedOwner.Name) || currentDerivedOwner.IdentityId == prevDerivedOwner.IdentityId {
							present = true
							break
						} else {
							if prevOwnerEmail, err := common.ParseAddress(prevDerivedOwner.Name); err == nil {
								if currentOwnerEmail, err := common.ParseAddress(currentDerivedOwner.Name); err == nil {
									if prevOwnerEmail.Address == currentOwnerEmail.Address {
										present = true
										break
									}
								}
							}
						}
					}
				}

				if !present {

					var (
						uniqueIdentities = make(map[string]struct{})
						eventInfo        = EventInfo{
							event:     prevDerivedOwner.Event.Name,
							region:    prevDerivedOwner.Event.Region,
							eventTime: prevDerivedOwner.Event.Time,
						}
					)

					prevDerivedOwner.Name = strings.TrimPrefix(prevDerivedOwner.Name, EX_EMPLOYEE_PREFIX)
					prevDerivedOwner.Name = strings.TrimPrefix(prevDerivedOwner.Name, INVALID_EMPLOYEE_PREFIX)

					if strings.HasSuffix(prevDerivedOwner.Name, SERVICEACCOUNT_USER_SUFFIX) {
						name := strings.TrimSuffix(prevDerivedOwner.Name, SERVICEACCOUNT_USER_SUFFIX)
						getServiceAccountOwnersForActivity(resourceContext, name, eventInfo, activityUsers, uniqueIdentities, "User owned service account "+name+" has performed activities on the resource", "b", *currentContextDoc)
					} else if strings.HasSuffix(prevDerivedOwner.Name, IAM_USER_SUFFIX) {
						name := strings.TrimSuffix(prevDerivedOwner.Name, IAM_USER_SUFFIX)
						getAWSIAMEntityOwnersForActivity(resourceContext, name, eventInfo, activityUsers, prevDerivedOwner.Event.IdentityAccount, common.AWS_IAM_USER_RESOURCE_TYPE, uniqueIdentities, "User owned IAM User "+name+" has performed activities on the resource", "b")
					} else if strings.HasSuffix(prevDerivedOwner.Name, IAM_ROLE_SUFFIX) {
						name := strings.TrimSuffix(prevDerivedOwner.Name, IAM_ROLE_SUFFIX)
						getAWSIAMEntityOwnersForActivity(resourceContext, name, eventInfo, activityUsers, prevDerivedOwner.Event.IdentityAccount, common.AWS_IAM_ROLE_RESOURCE_TYPE, uniqueIdentities, "User owned IAM Role "+name+" has performed activities on the resource", "b")
					} else if strings.HasSuffix(prevDerivedOwner.Name, APP_USER_SUFFIX) {
						name := strings.TrimSuffix(prevDerivedOwner.Name, APP_USER_SUFFIX)
						getApplicationOwnersForActivity(resourceContext, name, prevDerivedOwner.Event.IdentityAccount, eventInfo, activityUsers, uniqueIdentities, "User owned Application "+name+" has performed activities on the resource", "b", *currentContextDoc)
					} else if IsNonHumanEmail(prevDerivedOwner.Name, resourceContext) {
						getNonHumanEmailOwnersForActivity(resourceContext, prevDerivedOwner.Name, eventInfo, activityUsers, uniqueIdentities, "User owned group email "+prevDerivedOwner.Name+" has performed activities on the resource", "b")
					}

					activityUsers[eventInfo.eventTime+"a"+fmt.Sprintf("%03d", len(previousContextDoc.DerivedOwners)-i)] = resourceContext.GetUserContextItem(prevDerivedOwner.Name, prevDerivedOwner.Type, prevDerivedOwner.Desc, prevDerivedOwner.IdentityId, currentContextDoc.Account, &common.ResourceCtxEvent{
						Name:   eventInfo.event,
						Region: eventInfo.region,
						Time:   eventInfo.eventTime,
					})
				}
			}
		}
	}

	eventTimes := make([]string, 0, len(activityUsers))

	for k := range activityUsers {
		eventTimes = append(eventTimes, k)
	}

	sort.Sort(sort.Reverse(sort.StringSlice(eventTimes)))

	for _, eventTime := range eventTimes {

		activityUser := activityUsers[eventTime]

		activityUser.CollectedFrom = resourceContext.PreviousCollectedAt

		currentContextDoc.ResourceOwnerTypes.DerivedOwners = append(currentContextDoc.ResourceOwnerTypes.DerivedOwners, activityUser)
	}
}

func processPreviousContextForDeployments(resourceContext *ResourceContext, currentContextDoc *common.ResourceContextInsertDoc, previousContextDoc common.ResourceContextInsertDoc) {

	for _, prevDerivedDeployment := range previousContextDoc.ResourceDeploymentTypes.DerivedDeployment {

		if prevDerivedDeployment.Type == common.ACTIVITY_DEPLOYMENT_TYPE {

			var present bool

			for _, currentDerivedDeployment := range currentContextDoc.ResourceDeploymentTypes.DerivedDeployment {
				if strings.ToLower(currentDerivedDeployment.Name) == strings.ToLower(prevDerivedDeployment.Name) {
					present = true
					break
				}
			}

			if !present {
				currentContextDoc.ResourceDeploymentTypes.DerivedDeployment = append(currentContextDoc.ResourceDeploymentTypes.DerivedDeployment, common.ResourceContextItem{
					Name:          prevDerivedDeployment.Name,
					Type:          prevDerivedDeployment.Type,
					Desc:          prevDerivedDeployment.Desc,
					CollectedFrom: resourceContext.PreviousCollectedAt,
				})
			}
		}
	}
}

func processPreviousContextForApplications(resourceContext *ResourceContext, currentContextDoc *common.ResourceContextInsertDoc, previousContextDoc common.ResourceContextInsertDoc) {

	for _, prevDerivedApp := range previousContextDoc.ResourceAppTypes.DerivedApp {

		if prevDerivedApp.Type == common.ACTIVITY_APP_TYPE {

			var present bool

			for _, currentDerivedApp := range currentContextDoc.ResourceAppTypes.DerivedApp {
				if strings.ToLower(currentDerivedApp.Name) == strings.ToLower(prevDerivedApp.Name) {
					present = true
					break
				}
			}

			if !present {
				currentContextDoc.ResourceAppTypes.DerivedApp = append(currentContextDoc.ResourceAppTypes.DerivedApp, common.ResourceContextItem{
					Name:          prevDerivedApp.Name,
					Type:          prevDerivedApp.Type,
					Desc:          prevDerivedApp.Desc,
					CollectedFrom: resourceContext.PreviousCollectedAt,
				})
			}
		}
	}
}

func processPreviousContextForUserAgents(currentContextDoc *common.ResourceContextInsertDoc, previousContextDoc common.ResourceContextInsertDoc) {

	for _, prevUserAgent := range previousContextDoc.UserAgents {

		var present bool

		for _, currentUserAgent := range currentContextDoc.UserAgents {
			if strings.ToLower(currentUserAgent) == strings.ToLower(prevUserAgent) {
				present = true
				break
			}
		}

		if !present {
			currentContextDoc.UserAgents = append(currentContextDoc.UserAgents, prevUserAgent)
		}
	}
}
