package context

import (
	"encoding/json"
	"strconv"
	"strings"

	"github.com/precize/common"
	"github.com/precize/elastic"
	"github.com/precize/logger"
)

// ResourceContextCategorizationService handles categorization of resource context
type ResourceContextCategorizationService struct {
	TenantID    string
	CollectedAt string
	ServiceID   int
}

// NewResourceContextCategorizationService creates a new instance of the categorization service
func NewResourceContextCategorizationService(tenantID, collectedAt string, serviceID int) *ResourceContextCategorizationService {
	return &ResourceContextCategorizationService{
		TenantID:    tenantID,
		CollectedAt: collectedAt,
		ServiceID:   serviceID,
	}
}

// CategorizeResourceContexts processes all resource context records and categorizes them
func (s *ResourceContextCategorizationService) CategorizeResourceContexts() error {
	logger.Print(logger.INFO, "Starting resource context categorization", []string{s.TenantID}, s.CollectedAt)

	// Query resource context documents
	resourceContextDocs, err := s.fetchResourceContextDocs()
	if err != nil {
		logger.Print(logger.ERROR, "Error fetching resource context documents", []string{err.Error()})
		return err
	}

	logger.Print(logger.INFO, "Fetched resource context documents", []string{strconv.Itoa(len(resourceContextDocs))})

	// Process each document and categorize
	var bulkUpdateRequest strings.Builder
	for _, doc := range resourceContextDocs {
		categories := s.categorizeResource(doc)
		if len(categories) > 0 {
			doc.ContextCategories = categories

			// Prepare bulk update
			updateMetadata := `{"update": {"_id": "` + doc.ID + `"}}`
			updateDoc := `{"doc":{"contextCategories":["` + strings.Join(s.categoriesToStrings(categories), `","`) + `"]}}`

			bulkUpdateRequest.WriteString(updateMetadata)
			bulkUpdateRequest.WriteString("\n")
			bulkUpdateRequest.WriteString(updateDoc)
			bulkUpdateRequest.WriteString("\n")
		}
	}

	// Execute bulk update if there are updates
	if bulkUpdateRequest.Len() > 0 {
		if err := elastic.BulkDocumentsAPI(s.TenantID, elastic.RESOURCE_CONTEXT_INDEX, bulkUpdateRequest.String()); err != nil {
			logger.Print(logger.ERROR, "Error executing bulk update for resource context categorization", []string{err.Error()})
			return err
		}
		logger.Print(logger.INFO, "Successfully updated resource context categories", []string{s.TenantID})
	}

	return nil
}

// fetchResourceContextDocs retrieves all resource context documents for the given parameters
func (s *ResourceContextCategorizationService) fetchResourceContextDocs() ([]common.ResourceContextInsertDoc, error) {
	var (
		docs        []common.ResourceContextInsertDoc
		searchAfter any
		query       = `{"query":{"bool":{"must":[{"match":{"tenantId.keyword":"` + s.TenantID + `"}},{"term":{"lastCollectedAt":"` + s.CollectedAt + `"}},{"term":{"serviceId":` + strconv.Itoa(s.ServiceID) + `}}]}}}`
	)

	for {
		elasticDocs, sortResponse, err := elastic.ExecuteLargeSearchQuery([]string{elastic.RESOURCE_CONTEXT_INDEX}, query, searchAfter)
		if err != nil {
			return nil, err
		}

		if len(elasticDocs) > 0 {
			searchAfter = sortResponse
		} else {
			break
		}

		for _, elasticDoc := range elasticDocs {
			var doc common.ResourceContextInsertDoc
			jsonBytes, err := json.Marshal(elasticDoc)
			if err != nil {
				continue
			}

			if err := json.Unmarshal(jsonBytes, &doc); err != nil {
				continue
			}

			docs = append(docs, doc)
		}
	}

	return docs, nil
}

// categorizeResource determines the categories for a single resource context document
func (s *ResourceContextCategorizationService) categorizeResource(doc common.ResourceContextInsertDoc) []common.ResourceContextCategory {
	var categories []common.ResourceContextCategory

	// Check if customer has existing context (from tags or cust_entity_context)
	hasCustomerContext := s.hasCustomerDefinedContext(doc)

	// Check if we derived any context
	hasDerivedContext := s.hasDerivedContext(doc)

	// Check for contradictory context
	hasContradictoryContext := s.hasContradictoryContext(doc)

	// Apply categorization logic
	if hasContradictoryContext {
		categories = append(categories, common.CONTRADICTORY_CONTEXT)
	}

	if hasCustomerContext && hasDerivedContext {
		categories = append(categories, common.ENHANCED_EXISTING_CONTEXT)
	} else if !hasCustomerContext && hasDerivedContext {
		categories = append(categories, common.ALL_DERIVED_CONTEXT)
	} else if !hasCustomerContext && !hasDerivedContext {
		categories = append(categories, common.NO_DERIVED_CONTEXT)
	}

	return categories
}

// hasCustomerDefinedContext checks if the resource has customer-defined context
func (s *ResourceContextCategorizationService) hasCustomerDefinedContext(doc common.ResourceContextInsertDoc) bool {
	// Check for customer-defined context in various fields
	return s.hasCustomerDefinedOwners(doc) ||
		s.hasCustomerDefinedEnv(doc) ||
		s.hasCustomerDefinedApp(doc) ||
		s.hasCustomerDefinedSoftware(doc) ||
		s.hasCustomerDefinedDeployment(doc) ||
		s.hasCustomerDefinedSensitivity(doc) ||
		s.hasCustomerDefinedCompliance(doc) ||
		s.hasCustomerDefinedCostCenter(doc) ||
		s.hasCustomerDefinedTeam(doc) ||
		s.hasCustomerDefinedTTL(doc)
}

// hasCustomerDefinedOwners checks if there are customer-defined owners
func (s *ResourceContextCategorizationService) hasCustomerDefinedOwners(doc common.ResourceContextInsertDoc) bool {
	for _, owner := range doc.ResourceOwnerTypes.DefinedOwners {
		if s.isCustomerDefined(owner) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedEnv checks if there are customer-defined environments
func (s *ResourceContextCategorizationService) hasCustomerDefinedEnv(doc common.ResourceContextInsertDoc) bool {
	for _, env := range doc.ResourceEnvTypes.DefinedEnv {
		if s.isCustomerDefined(env) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedApp checks if there are customer-defined applications
func (s *ResourceContextCategorizationService) hasCustomerDefinedApp(doc common.ResourceContextInsertDoc) bool {
	for _, app := range doc.ResourceAppTypes.DefinedApp {
		if s.isCustomerDefined(app) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedSoftware checks if there are customer-defined software
func (s *ResourceContextCategorizationService) hasCustomerDefinedSoftware(doc common.ResourceContextInsertDoc) bool {
	for _, software := range doc.ResourceSoftwareTypes.DefinedSoftware {
		if s.isCustomerDefined(software) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedDeployment checks if there are customer-defined deployments
func (s *ResourceContextCategorizationService) hasCustomerDefinedDeployment(doc common.ResourceContextInsertDoc) bool {
	for _, deployment := range doc.ResourceDeploymentTypes.DefinedDeployment {
		if s.isCustomerDefined(deployment) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedSensitivity checks if there are customer-defined sensitivities
func (s *ResourceContextCategorizationService) hasCustomerDefinedSensitivity(doc common.ResourceContextInsertDoc) bool {
	for _, sensitivity := range doc.ResourceSensitivityTypes.DefinedSensitivity {
		if s.isCustomerDefined(sensitivity) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedCompliance checks if there are customer-defined compliances
func (s *ResourceContextCategorizationService) hasCustomerDefinedCompliance(doc common.ResourceContextInsertDoc) bool {
	for _, compliance := range doc.ResourceComplianceTypes.DefinedCompliance {
		if s.isCustomerDefined(compliance) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedCostCenter checks if there are customer-defined cost centers
func (s *ResourceContextCategorizationService) hasCustomerDefinedCostCenter(doc common.ResourceContextInsertDoc) bool {
	for _, costCenter := range doc.ResourceCostCenterTypes.DefinedCostCenter {
		if s.isCustomerDefined(costCenter) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedTeam checks if there are customer-defined teams
func (s *ResourceContextCategorizationService) hasCustomerDefinedTeam(doc common.ResourceContextInsertDoc) bool {
	for _, team := range doc.ResourceTeamTypes.DefinedTeam {
		if s.isCustomerDefined(team) {
			return true
		}
	}
	return false
}

// hasCustomerDefinedTTL checks if there are customer-defined TTLs
func (s *ResourceContextCategorizationService) hasCustomerDefinedTTL(doc common.ResourceContextInsertDoc) bool {
	for _, ttl := range doc.ResourceTTLTypes.DefinedTTL {
		if s.isCustomerDefined(ttl) {
			return true
		}
	}
	return false
}

// isCustomerDefined checks if a ResourceContextItem is customer-defined
// Customer context comes from tags or cust_entity_context (updatedBy should <NAME_EMAIL> or support_...)
func (s *ResourceContextCategorizationService) isCustomerDefined(item common.ResourceContextItem) bool {
	// Check if it's from tags (tag-based context)
	if strings.HasPrefix(item.Type, "tag_") {
		return true
	}

	// Check if it's customer-defined user type
	if item.Type == common.CUSTOMER_DEFINED_USER_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_ENV_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_APP_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_SOFTWARE_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_DEPLOYMENT_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_SENSITIVITY_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_COMPLIANCE_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_COSTCENTER_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_TEAM_TYPE ||
		item.Type == common.CUSTOMER_DEFINED_TTL_TYPE {
		return true
	}

	// Check description for customer-defined context (contains "by" followed by non-admin/support user)
	if strings.Contains(item.Desc, " by ") {
		parts := strings.Split(item.Desc, " by ")
		if len(parts) > 1 {
			updatedBy := strings.TrimSpace(parts[len(parts)-1])
			// Not customer-defined if <NAME_EMAIL> or support_*
			if updatedBy == "<EMAIL>" || strings.HasPrefix(updatedBy, "support_") {
				return false
			}
			return true
		}
	}

	return false
}

// hasDerivedContext checks if the resource has any derived context
func (s *ResourceContextCategorizationService) hasDerivedContext(doc common.ResourceContextInsertDoc) bool {
	return len(doc.ResourceOwnerTypes.DerivedOwners) > 0 ||
		len(doc.ResourceEnvTypes.DerivedEnv) > 0 ||
		len(doc.ResourceAppTypes.DerivedApp) > 0 ||
		len(doc.ResourceSoftwareTypes.DerivedSoftware) > 0 ||
		len(doc.ResourceDeploymentTypes.DerivedDeployment) > 0 ||
		len(doc.ResourceSensitivityTypes.DerivedSensitivity) > 0 ||
		len(doc.ResourceComplianceTypes.DerivedCompliance) > 0 ||
		len(doc.ResourceTeamTypes.DerivedTeam) > 0 ||
		len(doc.ResourceTTLTypes.DerivedTTL) > 0
}

// hasContradictoryContext checks if there are contradictions between customer-defined and derived context
func (s *ResourceContextCategorizationService) hasContradictoryContext(doc common.ResourceContextInsertDoc) bool {
	// Check for contradictions in owners
	if s.hasOwnerContradictions(doc) {
		return true
	}

	// Check for contradictions in environments
	if s.hasEnvContradictions(doc) {
		return true
	}

	// Check for contradictions in applications
	if s.hasAppContradictions(doc) {
		return true
	}

	// Check for contradictions in other context types
	if s.hasSoftwareContradictions(doc) ||
		s.hasDeploymentContradictions(doc) ||
		s.hasSensitivityContradictions(doc) ||
		s.hasComplianceContradictions(doc) ||
		s.hasTeamContradictions(doc) ||
		s.hasTTLContradictions(doc) {
		return true
	}

	return false
}

// hasOwnerContradictions checks for contradictions in owner context
func (s *ResourceContextCategorizationService) hasOwnerContradictions(doc common.ResourceContextInsertDoc) bool {
	customerOwners := make(map[string]bool)
	derivedOwners := make(map[string]bool)

	// Collect customer-defined owners
	for _, owner := range doc.ResourceOwnerTypes.DefinedOwners {
		if s.isCustomerDefined(owner) {
			customerOwners[strings.ToLower(owner.Name)] = true
		}
	}

	// Collect derived owners
	for _, owner := range doc.ResourceOwnerTypes.DerivedOwners {
		derivedOwners[strings.ToLower(owner.Name)] = true
	}

	// Check if there are different owners defined by customer vs derived
	if len(customerOwners) > 0 && len(derivedOwners) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerOwner := range customerOwners {
			if derivedOwners[customerOwner] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// hasEnvContradictions checks for contradictions in environment context
func (s *ResourceContextCategorizationService) hasEnvContradictions(doc common.ResourceContextInsertDoc) bool {
	customerEnvs := make(map[string]bool)
	derivedEnvs := make(map[string]bool)

	// Collect customer-defined environments
	for _, env := range doc.ResourceEnvTypes.DefinedEnv {
		if s.isCustomerDefined(env) {
			customerEnvs[strings.ToLower(env.Name)] = true
		}
	}

	// Collect derived environments
	for _, env := range doc.ResourceEnvTypes.DerivedEnv {
		derivedEnvs[strings.ToLower(env.Name)] = true
	}

	// Check if there are different environments defined by customer vs derived
	if len(customerEnvs) > 0 && len(derivedEnvs) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerEnv := range customerEnvs {
			if derivedEnvs[customerEnv] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// hasAppContradictions checks for contradictions in application context
func (s *ResourceContextCategorizationService) hasAppContradictions(doc common.ResourceContextInsertDoc) bool {
	customerApps := make(map[string]bool)
	derivedApps := make(map[string]bool)

	// Collect customer-defined applications
	for _, app := range doc.ResourceAppTypes.DefinedApp {
		if s.isCustomerDefined(app) {
			customerApps[strings.ToLower(app.Name)] = true
		}
	}

	// Collect derived applications
	for _, app := range doc.ResourceAppTypes.DerivedApp {
		derivedApps[strings.ToLower(app.Name)] = true
	}

	// Check if there are different applications defined by customer vs derived
	if len(customerApps) > 0 && len(derivedApps) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerApp := range customerApps {
			if derivedApps[customerApp] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// hasSoftwareContradictions checks for contradictions in software context
func (s *ResourceContextCategorizationService) hasSoftwareContradictions(doc common.ResourceContextInsertDoc) bool {
	customerSoftware := make(map[string]bool)
	derivedSoftware := make(map[string]bool)

	// Collect customer-defined software
	for _, software := range doc.ResourceSoftwareTypes.DefinedSoftware {
		if s.isCustomerDefined(software) {
			customerSoftware[strings.ToLower(software.Name)] = true
		}
	}

	// Collect derived software
	for _, software := range doc.ResourceSoftwareTypes.DerivedSoftware {
		derivedSoftware[strings.ToLower(software.Name)] = true
	}

	// Check if there are different software defined by customer vs derived
	if len(customerSoftware) > 0 && len(derivedSoftware) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerSoft := range customerSoftware {
			if derivedSoftware[customerSoft] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// hasDeploymentContradictions checks for contradictions in deployment context
func (s *ResourceContextCategorizationService) hasDeploymentContradictions(doc common.ResourceContextInsertDoc) bool {
	customerDeployments := make(map[string]bool)
	derivedDeployments := make(map[string]bool)

	// Collect customer-defined deployments
	for _, deployment := range doc.ResourceDeploymentTypes.DefinedDeployment {
		if s.isCustomerDefined(deployment) {
			customerDeployments[strings.ToLower(deployment.Name)] = true
		}
	}

	// Collect derived deployments
	for _, deployment := range doc.ResourceDeploymentTypes.DerivedDeployment {
		derivedDeployments[strings.ToLower(deployment.Name)] = true
	}

	// Check if there are different deployments defined by customer vs derived
	if len(customerDeployments) > 0 && len(derivedDeployments) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerDep := range customerDeployments {
			if derivedDeployments[customerDep] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// hasSensitivityContradictions checks for contradictions in sensitivity context
func (s *ResourceContextCategorizationService) hasSensitivityContradictions(doc common.ResourceContextInsertDoc) bool {
	customerSensitivities := make(map[string]bool)
	derivedSensitivities := make(map[string]bool)

	// Collect customer-defined sensitivities
	for _, sensitivity := range doc.ResourceSensitivityTypes.DefinedSensitivity {
		if s.isCustomerDefined(sensitivity) {
			customerSensitivities[strings.ToLower(sensitivity.Name)] = true
		}
	}

	// Collect derived sensitivities
	for _, sensitivity := range doc.ResourceSensitivityTypes.DerivedSensitivity {
		derivedSensitivities[strings.ToLower(sensitivity.Name)] = true
	}

	// Check if there are different sensitivities defined by customer vs derived
	if len(customerSensitivities) > 0 && len(derivedSensitivities) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerSens := range customerSensitivities {
			if derivedSensitivities[customerSens] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// hasComplianceContradictions checks for contradictions in compliance context
func (s *ResourceContextCategorizationService) hasComplianceContradictions(doc common.ResourceContextInsertDoc) bool {
	customerCompliances := make(map[string]bool)
	derivedCompliances := make(map[string]bool)

	// Collect customer-defined compliances
	for _, compliance := range doc.ResourceComplianceTypes.DefinedCompliance {
		if s.isCustomerDefined(compliance) {
			customerCompliances[strings.ToLower(compliance.Name)] = true
		}
	}

	// Collect derived compliances
	for _, compliance := range doc.ResourceComplianceTypes.DerivedCompliance {
		derivedCompliances[strings.ToLower(compliance.Name)] = true
	}

	// Check if there are different compliances defined by customer vs derived
	if len(customerCompliances) > 0 && len(derivedCompliances) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerComp := range customerCompliances {
			if derivedCompliances[customerComp] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// hasTeamContradictions checks for contradictions in team context
func (s *ResourceContextCategorizationService) hasTeamContradictions(doc common.ResourceContextInsertDoc) bool {
	customerTeams := make(map[string]bool)
	derivedTeams := make(map[string]bool)

	// Collect customer-defined teams
	for _, team := range doc.ResourceTeamTypes.DefinedTeam {
		if s.isCustomerDefined(team) {
			customerTeams[strings.ToLower(team.Name)] = true
		}
	}

	// Collect derived teams
	for _, team := range doc.ResourceTeamTypes.DerivedTeam {
		derivedTeams[strings.ToLower(team.Name)] = true
	}

	// Check if there are different teams defined by customer vs derived
	if len(customerTeams) > 0 && len(derivedTeams) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerTeam := range customerTeams {
			if derivedTeams[customerTeam] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// hasTTLContradictions checks for contradictions in TTL context
func (s *ResourceContextCategorizationService) hasTTLContradictions(doc common.ResourceContextInsertDoc) bool {
	customerTTLs := make(map[string]bool)
	derivedTTLs := make(map[string]bool)

	// Collect customer-defined TTLs
	for _, ttl := range doc.ResourceTTLTypes.DefinedTTL {
		if s.isCustomerDefined(ttl) {
			customerTTLs[strings.ToLower(ttl.Name)] = true
		}
	}

	// Collect derived TTLs
	for _, ttl := range doc.ResourceTTLTypes.DerivedTTL {
		derivedTTLs[strings.ToLower(ttl.Name)] = true
	}

	// Check if there are different TTLs defined by customer vs derived
	if len(customerTTLs) > 0 && len(derivedTTLs) > 0 {
		// If there's no overlap, it's contradictory
		hasOverlap := false
		for customerTTL := range customerTTLs {
			if derivedTTLs[customerTTL] {
				hasOverlap = true
				break
			}
		}
		return !hasOverlap
	}

	return false
}

// categoriesToStrings converts ResourceContextCategory slice to string slice
func (s *ResourceContextCategorizationService) categoriesToStrings(categories []common.ResourceContextCategory) []string {
	result := make([]string, len(categories))
	for i, category := range categories {
		result[i] = string(category)
	}
	return result
}
