package context

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/precize/common"
	"github.com/precize/logger"
)

type AWSEvent struct {
	UserIdentity *UserIdentity `json:"userIdentity"`
}

type UserIdentity struct {
	Type           string          `json:"type"`
	SessionContext *SessionContext `json:"sessionContext,omitempty"`
	UserName       string          `json:"userName,omitempty"`
	AccountID      string          `json:"accountId,omitempty"`
}

type SessionContext struct {
	SessionIssuer *SessionIssuer `json:"sessionIssuer"`
}

type SessionIssuer struct {
	Arn  string `json:"arn"`
	Type string `json:"type"`
}

func parseAWSActivity(resourceContext *ResourceContext, eventInfo EventInfo,
	activityUsers map[string]common.ResourceContextItem, byPrincipal AggsByPrincipal, resourceContextInsertDoc common.ResourceContextInsertDoc) {

	var awsEvent AWSEvent
	err := json.Unmarshal([]byte(eventInfo.eventJSONString), &awsEvent)
	if err != nil {
		logger.Print(logger.ERROR, "Got error unmarshalling", []string{resourceContext.TenantID, eventInfo.account}, err)
		return
	}

	userIdentity := awsEvent.UserIdentity

	for _, bucket := range byPrincipal.Buckets {

		principalID := bucket.Key

		for _, principalInnerHit := range bucket.MatchedDoc.Hits.Hits {

			principalType := principalInnerHit.Source.AccessKeyPrincipalType
			principalAccountID := principalInnerHit.Source.AccessKeyPrincipalAccountID
			principalEventTime := principalInnerHit.Source.EventTime
			prinicipalEvent := principalInnerHit.Source.EventName
			principalEventRegion := principalInnerHit.Source.Region

			var assumedRoleString string

			if userIdentity != nil && userIdentity.Type == "AssumedRole" {
				assumedRoleString = " via Assumed Role"
			}

			switch principalType {
			case "IAMUser":

				uniqueIdentities := make(map[string]struct{})
				getAWSIAMEntityOwnersForActivity(resourceContext, principalID, eventInfo, activityUsers, principalAccountID,
					common.AWS_IAM_USER_RESOURCE_TYPE, uniqueIdentities, "User owned IAM User "+principalID+" has performed activities on the resource", "b")

				// Add IAM User as an owner of this resource via Activity
				activityUsers[principalEventTime+"b"] = resourceContext.GetUserContextItem(principalID+IAM_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
					"IAM User from internal account "+principalAccountID+" has performed activities on the resource"+assumedRoleString, principalID, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:            prinicipalEvent,
						Region:          principalEventRegion,
						Time:            principalEventTime,
						IdentityAccount: principalAccountID,
					})

			case "AWSAccount":

				activityUsers[principalEventTime] = resourceContext.GetUserContextItem(principalID+ACCOUNT_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
					"Identity from external AWS account has performed activities on the resource"+assumedRoleString, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:   prinicipalEvent,
						Region: principalEventRegion,
						Time:   principalEventTime,
					})

			case "AWSService":

				activityUsers[principalEventTime] = resourceContext.GetUserContextItem(principalID+AWSSERVICE_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
					"AWS internal service has performed activities on the resource"+assumedRoleString, "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:   prinicipalEvent,
						Region: principalEventRegion,
						Time:   principalEventTime,
					})

			case "AssumedRole":

				roleSplit := strings.Split(principalID, "/")
				role := roleSplit[len(roleSplit)-1]

				uniqueIdentities := make(map[string]struct{})
				getAWSIAMEntityOwnersForActivity(resourceContext, role, eventInfo, activityUsers, principalAccountID,
					common.AWS_IAM_ROLE_RESOURCE_TYPE, uniqueIdentities, "User owned IAM Role "+role+" has performed activities on the resource", "b")

				// Add IAM Role as an owner of this resource via Activity
				activityUsers[principalEventTime+"b"] = resourceContext.GetUserContextItem(role+IAM_ROLE_SUFFIX, common.ACTIVITY_USER_TYPE,
					"IAM Role has performed activities on the resource"+assumedRoleString, role, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:            prinicipalEvent,
						Region:          principalEventRegion,
						Time:            principalEventTime,
						IdentityAccount: principalAccountID,
					})
			}
		}
	}

	if userIdentity != nil {
		if sessionContext := userIdentity.SessionContext; sessionContext != nil {
			if sessionIssuer := sessionContext.SessionIssuer; sessionIssuer != nil {

				sessionIssuerType := sessionIssuer.Type
				if sessionIssuerType == "Role" {

					sessionIssuerARN := sessionIssuer.Arn

					if strings.Contains(sessionIssuerARN, "/aws-service-role/") || strings.Contains(sessionIssuerARN, "/service-role/") {
						// AWS Service Role
						serviceRoleSplit := strings.Split(sessionIssuerARN, "/")
						serviceRole := serviceRoleSplit[len(serviceRoleSplit)-1]

						uniqueIdentities := make(map[string]struct{})
						getAWSIAMEntityOwnersForActivity(resourceContext, serviceRole, eventInfo, activityUsers, eventInfo.account,
							common.AWS_IAM_ROLE_RESOURCE_TYPE, uniqueIdentities, "User owned AWS service role "+serviceRole+" has performed activities on the resource", "a")

						activityUsers[eventInfo.eventTime] = resourceContext.GetUserContextItem(serviceRole+IAM_ROLE_SUFFIX, common.ACTIVITY_USER_TYPE,
							"AWS Internal service role has performed activities on the resource", "", resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
								Name:            eventInfo.event,
								Region:          eventInfo.region,
								Time:            eventInfo.eventTime,
								IdentityAccount: eventInfo.account,
							})
						return
					}

					if strings.Contains(sessionIssuerARN, "sso.amazonaws.com") {
						// SSO User - username will be the mapped sso username

						var (
							user    = eventInfo.username
							viaUser = ""
						)

						if parentIdentity, ok := resourceContext.GetChildPrimaryEmail(eventInfo.username); ok && parentIdentity != eventInfo.username {
							viaUser = " using mapped sso username " + eventInfo.username
						}

						activityUsers[eventInfo.eventTime] = resourceContext.GetUserContextItem(user, common.ACTIVITY_USER_TYPE,
							"User has performed activities on the resource"+viaUser, eventInfo.username, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
								Name:   eventInfo.event,
								Region: eventInfo.region,
								Time:   eventInfo.eventTime,
							})
						return
					}

					if enabled, ok := resourceContext.GetEnabledService("okta"); ok && enabled {
						if idpUsers, ok := resourceContext.GetIDPMappedUsers(eventInfo.username); ok {
							// Okta User
							for idpUser := range idpUsers {
								if idpUser != eventInfo.username {
									activityUsers[eventInfo.eventTime+idpUser] = resourceContext.GetUserContextItem(idpUser, common.ACTIVITY_USER_TYPE,
										"User has performed activities on the resource using mapped okta username "+eventInfo.username, eventInfo.username, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
											Name:   eventInfo.event,
											Region: eventInfo.region,
											Time:   eventInfo.eventTime,
										})

									// Activity Identity should be created with username and linked to parent idp user using identity merging
									resourceContext.SetChildPrimaryEmail(eventInfo.username, idpUser)
								}
							}

							return
						}
					}

					if len(sessionIssuerARN) > 0 {

						roleSplit := strings.Split(sessionIssuerARN, "/")
						role := roleSplit[len(roleSplit)-1]

						uniqueIdentities := make(map[string]struct{})
						getAWSIAMEntityOwnersForActivity(resourceContext, role, eventInfo, activityUsers, eventInfo.account,
							common.AWS_IAM_ROLE_RESOURCE_TYPE, uniqueIdentities, "User owned IAM Role "+role+" has performed activities on the resource", "a")

						// Add IAM Role as an owner of this resource via Activity
						activityUsers[eventInfo.eventTime+"a"] = resourceContext.GetUserContextItem(role+IAM_ROLE_SUFFIX, common.ACTIVITY_USER_TYPE,
							"IAM Role has performed activities on the resource", role, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
								Name:            eventInfo.event,
								Region:          eventInfo.region,
								Time:            eventInfo.eventTime,
								IdentityAccount: eventInfo.account,
							})
					}
				}
			}
		}

		if userIdentity.Type == "IAMUser" {

			if len(awsEvent.UserIdentity.UserName) > 0 && len(awsEvent.UserIdentity.AccountID) > 0 {

				uniqueIdentities := make(map[string]struct{})
				getAWSIAMEntityOwnersForActivity(resourceContext, awsEvent.UserIdentity.UserName, eventInfo, activityUsers, eventInfo.account,
					common.AWS_IAM_USER_RESOURCE_TYPE, uniqueIdentities, "User owned IAM User "+awsEvent.UserIdentity.UserName+" has performed activities on the resource", "a")

				// Add IAM User as an owner of this resource via Activity
				activityUsers[eventInfo.eventTime+"a"] = resourceContext.GetUserContextItem(awsEvent.UserIdentity.UserName+IAM_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
					"IAM User has performed activities on the resource", awsEvent.UserIdentity.UserName, resourceContextInsertDoc.Account, &common.ResourceCtxEvent{
						Name:            eventInfo.event,
						Region:          eventInfo.region,
						Time:            eventInfo.eventTime,
						IdentityAccount: eventInfo.account,
					})
			}
		}
	}
}

func getAWSIAMEntityOwnersForActivity(resourceContext *ResourceContext, userOrRole string, eventInfo EventInfo, activityUsers map[string]common.ResourceContextItem,
	iamAccount, entityType string, uniqueIdentities map[string]struct{}, desc, sortAlphabet string) {

	if _, ok := uniqueIdentities[userOrRole]; ok {
		return
	}

	uniqueIdentities[userOrRole] = struct{}{}
	depth := len(sortAlphabet)

	userOrRoleContextID := common.GenerateCombinedHashID(userOrRole, entityType, iamAccount, resourceContext.LastCollectedAt, resourceContext.TenantID)

	if userOrRoleDoc, ok := resourceContext.GetResourceContextInsertDoc(userOrRoleContextID); ok {

		// Resourcename user will not be derived for the service identity at this time. Additional entry for it if exists
		name := userOrRole
		if len(userOrRoleDoc.ResourceName) > 0 {
			name = userOrRoleDoc.ResourceName
		}

		activityUsers[eventInfo.eventTime+sortAlphabet+"c"] = resourceContext.GetUserContextItem(name+PLACEHOLDER_USER_SUFFIX, common.ACTIVITY_USER_TYPE,
			desc, "", userOrRoleDoc.Account, &common.ResourceCtxEvent{
				Name:          eventInfo.event,
				Region:        eventInfo.region,
				Time:          eventInfo.eventTime,
				IndirectEvent: true,
			})

		for i, defined := range userOrRoleDoc.DefinedOwners {

			if strings.HasSuffix(defined.Name, ACCOUNT_USER_SUFFIX) || strings.HasSuffix(defined.Name, AWSSERVICE_USER_SUFFIX) {
				continue
			}

			if strings.HasSuffix(defined.Name, IAM_USER_SUFFIX) {
				if depth <= 5 {
					definedName := strings.TrimSuffix(defined.Name, IAM_USER_SUFFIX)
					// Recursive call
					getAWSIAMEntityOwnersForActivity(resourceContext, definedName, eventInfo, activityUsers, iamAccount, common.AWS_IAM_USER_RESOURCE_TYPE, uniqueIdentities, getIdentityChainDescription(desc, definedName, depth), sortAlphabet+sortAlphabet)
				}
			} else if strings.HasSuffix(defined.Name, IAM_ROLE_SUFFIX) {
				if depth <= 5 {
					definedName := strings.TrimSuffix(defined.Name, IAM_ROLE_SUFFIX)
					// Recursive call
					getAWSIAMEntityOwnersForActivity(resourceContext, definedName, eventInfo, activityUsers, iamAccount, common.AWS_IAM_ROLE_RESOURCE_TYPE, uniqueIdentities, getIdentityChainDescription(desc, definedName, depth), sortAlphabet+sortAlphabet)
				}
			} else if IsNonHumanEmail(defined.Name, resourceContext) {
				uniqueGroupEmailIdentities := make(map[string]struct{})
				getNonHumanEmailOwnersForActivity(resourceContext, defined.Name, eventInfo, activityUsers, uniqueGroupEmailIdentities, "User owned group email "+defined.Name+" has performed activities on the resource", sortAlphabet+sortAlphabet)
			} else {
				if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
					uniqueIdentities[defined.Name] = struct{}{}
					activityUsers[eventInfo.eventTime+sortAlphabet+"b"+fmt.Sprintf("%03d", len(userOrRoleDoc.DefinedOwners)-i)] = resourceContext.GetUserContextItem(defined.Name, common.ACTIVITY_USER_TYPE,
						desc, "", userOrRoleDoc.Account, &common.ResourceCtxEvent{
							Name:          eventInfo.event,
							Region:        eventInfo.region,
							Time:          eventInfo.eventTime,
							IndirectEvent: true,
						})
				}
			}
		}

		for i, derived := range userOrRoleDoc.DerivedOwners {

			if strings.HasSuffix(derived.Name, ACCOUNT_USER_SUFFIX) || strings.HasSuffix(derived.Name, AWSSERVICE_USER_SUFFIX) {
				continue
			}

			if strings.HasSuffix(derived.Name, IAM_USER_SUFFIX) {
				if depth <= 5 {
					// Recursive call
					derivedName := strings.TrimSuffix(derived.Name, IAM_USER_SUFFIX)
					getAWSIAMEntityOwnersForActivity(resourceContext, derivedName, eventInfo, activityUsers, iamAccount, common.AWS_IAM_USER_RESOURCE_TYPE, uniqueIdentities, getIdentityChainDescription(desc, derivedName, depth), sortAlphabet+sortAlphabet)
				}
			} else if strings.HasSuffix(derived.Name, IAM_ROLE_SUFFIX) {
				if depth <= 5 {
					// Recursive call
					derivedName := strings.TrimSuffix(derived.Name, IAM_ROLE_SUFFIX)
					getAWSIAMEntityOwnersForActivity(resourceContext, derivedName, eventInfo, activityUsers, iamAccount, common.AWS_IAM_ROLE_RESOURCE_TYPE, uniqueIdentities, getIdentityChainDescription(desc, derivedName, depth), sortAlphabet+sortAlphabet)
				}
			} else if IsNonHumanEmail(derived.Name, resourceContext) {
				getNonHumanEmailOwnersForActivity(resourceContext, derived.Name, eventInfo, activityUsers, uniqueIdentities, "User owned group email "+derived.Name+" has performed activities on the resource", sortAlphabet+sortAlphabet)
			} else {
				if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
					uniqueIdentities[derived.Name] = struct{}{}
					activityUsers[eventInfo.eventTime+sortAlphabet+"a"+fmt.Sprintf("%03d", len(userOrRoleDoc.DerivedOwners)-i)] = resourceContext.GetUserContextItem(derived.Name, common.ACTIVITY_USER_TYPE,
						desc, "", userOrRoleDoc.Account, &common.ResourceCtxEvent{
							Name:          eventInfo.event,
							Region:        eventInfo.region,
							Time:          eventInfo.eventTime,
							IndirectEvent: true,
						})
				}
			}
		}
	}
}

func getAWSIAMEntityOwnersForPolicy(resourceContext *ResourceContext, userOrRole, iamUserAccount, entityType string,
	resourceContextInsertDoc *common.ResourceContextInsertDoc, uniqueIdentities map[string]struct{}, desc string, depth int) {

	if _, ok := uniqueIdentities[userOrRole]; ok {
		return
	}

	uniqueIdentities[userOrRole] = struct{}{}
	userOrRoleContextID := common.GenerateCombinedHashID(userOrRole, entityType, iamUserAccount, resourceContext.LastCollectedAt, resourceContext.TenantID)

	if userOrRoleDoc, ok := resourceContext.GetResourceContextInsertDoc(userOrRoleContextID); ok {

		// Resourcename user will not be derived for the service identity at this time. Additional entry for it if exists
		name := userOrRole
		if len(userOrRoleDoc.ResourceName) > 0 {
			name = userOrRoleDoc.ResourceName
		}

		resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
			resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(name+PLACEHOLDER_USER_SUFFIX, common.POLICYBINDING_USER_TYPE,
				desc, "", resourceContextInsertDoc.Account, nil))

		for _, defined := range userOrRoleDoc.DefinedOwners {

			if strings.HasSuffix(defined.Name, ACCOUNT_USER_SUFFIX) || strings.HasSuffix(defined.Name, AWSSERVICE_USER_SUFFIX) {
				continue
			}

			if strings.HasSuffix(defined.Name, IAM_ROLE_SUFFIX) {
				if depth <= 5 {
					definedName := strings.TrimSuffix(defined.Name, IAM_ROLE_SUFFIX)
					// Recursive call
					getAWSIAMEntityOwnersForPolicy(resourceContext, definedName, iamUserAccount, entityType,
						resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, definedName, depth), depth+1)
				}
			} else if strings.HasSuffix(defined.Name, IAM_USER_SUFFIX) {
				if depth <= 5 {
					definedName := strings.TrimSuffix(defined.Name, IAM_USER_SUFFIX)
					// Recursive call
					getAWSIAMEntityOwnersForPolicy(resourceContext, definedName, iamUserAccount, entityType,
						resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, definedName, depth), depth+1)
				}
			} else if IsNonHumanEmail(defined.Name, resourceContext) {
				uniqueGroupEmailIdentities := make(map[string]struct{})
				getNonHumanEmailOwners(resourceContext, defined.Name, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+defined.Name+" has been added as a trusted entity for the resource", depth+1)
			} else {
				if _, ok := uniqueIdentities[defined.Name]; !ok || depth == 1 {
					uniqueIdentities[defined.Name] = struct{}{}
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(defined.Name, common.POLICYBINDING_USER_TYPE,
							desc, "", resourceContextInsertDoc.Account, nil))
				}
			}
		}

		for _, derived := range userOrRoleDoc.DerivedOwners {

			if strings.HasSuffix(derived.Name, ACCOUNT_USER_SUFFIX) || strings.HasSuffix(derived.Name, AWSSERVICE_USER_SUFFIX) {
				continue
			}

			if strings.HasSuffix(derived.Name, IAM_ROLE_SUFFIX) {
				if depth <= 5 {
					derivedName := strings.TrimSuffix(derived.Name, IAM_ROLE_SUFFIX)
					// Recursive call
					getAWSIAMEntityOwnersForPolicy(resourceContext, derivedName, iamUserAccount, entityType,
						resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, derivedName, depth), depth+1)
				}
			} else if strings.HasSuffix(derived.Name, IAM_USER_SUFFIX) {
				if depth <= 5 {
					derivedName := strings.TrimSuffix(derived.Name, IAM_USER_SUFFIX)
					// Recursive call
					getAWSIAMEntityOwnersForPolicy(resourceContext, derivedName, iamUserAccount, entityType,
						resourceContextInsertDoc, uniqueIdentities, getIdentityChainDescription(desc, derivedName, depth), depth+1)
				}
			} else if IsNonHumanEmail(derived.Name, resourceContext) {
				uniqueGroupEmailIdentities := make(map[string]struct{})
				getNonHumanEmailOwners(resourceContext, derived.Name, resourceContextInsertDoc, uniqueGroupEmailIdentities, "User owned group email "+derived.Name+" has been added as a trusted entity for the resource", depth+1)
			} else {
				if _, ok := uniqueIdentities[derived.Name]; !ok || depth == 1 {
					uniqueIdentities[derived.Name] = struct{}{}
					resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners = append(
						resourceContextInsertDoc.ResourceOwnerTypes.DerivedOwners, resourceContext.GetUserContextItem(derived.Name, common.POLICYBINDING_USER_TYPE,
							desc, "", resourceContextInsertDoc.Account, nil))
				}
			}
		}
	}
}

func getAWSEventActor(principalType string, username *string, eventJSONString, principalID string, resourceContext *ResourceContext) (actorType string) {

	if len(principalType) > 0 {
		switch principalType {
		case "IAMUser":
			*username = principalID + IAM_USER_SUFFIX
			actorType = "IAM User"
		case "AssumedRole":
			roleSplit := strings.Split(principalID, "/")
			role := roleSplit[len(roleSplit)-1]

			*username = role + IAM_ROLE_SUFFIX
			actorType = "IAM Role"
		case "AWSAccount":
			*username = principalID + ACCOUNT_USER_SUFFIX
			actorType = "AWS Account"
		case "AWSService":
			*username = principalID + AWSSERVICE_USER_SUFFIX
			actorType = "AWS Service"
		default:
			actorType = ""
		}

		return
	}

	var awsEvent AWSEvent
	err := json.Unmarshal([]byte(eventJSONString), &awsEvent)
	if err != nil {
		return
	}

	userIdentity := awsEvent.UserIdentity

	if userIdentity != nil {
		if sessionContext := userIdentity.SessionContext; sessionContext != nil {
			if sessionIssuer := sessionContext.SessionIssuer; sessionIssuer != nil {

				sessionIssuerType := sessionIssuer.Type
				if sessionIssuerType == "Role" {

					sessionIssuerARN := sessionIssuer.Arn
					if strings.Contains(sessionIssuerARN, "/aws-service-role/") || strings.Contains(sessionIssuerARN, "/service-role/") {
						serviceRoleSplit := strings.Split(sessionIssuerARN, "/")
						serviceRole := serviceRoleSplit[len(serviceRoleSplit)-1]

						*username = serviceRole + IAM_ROLE_SUFFIX
						actorType = "AWS Internal service role"

						return
					}

					if strings.Contains(sessionIssuerARN, "sso.amazonaws.com") {
						actorType = "SSO User"

						return
					}

					if enabled, ok := resourceContext.GetEnabledService("okta"); ok && enabled {
						if oktaUsers, ok := resourceContext.GetIDPMappedUsers(*username); ok {
							for oktaUser := range oktaUsers {
								if oktaUser != *username {
									*username = oktaUser
									break
								}
							}
							actorType = "Okta User"
							return
						}
					}

					if len(sessionIssuerARN) > 0 {

						roleSplit := strings.Split(sessionIssuerARN, "/")
						role := roleSplit[len(roleSplit)-1]

						*username = role + IAM_ROLE_SUFFIX
						actorType = "IAM Role"
					}
				}
			}
		}

		if len(actorType) > 0 {
			return
		}
	}

	if userIdentity.Type == "IAMUser" {
		*username = awsEvent.UserIdentity.UserName + IAM_USER_SUFFIX
		actorType = "IAM User"
	}

	return
}
